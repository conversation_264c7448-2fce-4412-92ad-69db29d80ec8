package mysort

// 快速排序（优化版本）
func QuickSort(arr []int) []int {
	if len(arr) <= 1 {
		return arr
	}
	result := make([]int, len(arr))
	copy(result, arr)
	quickSortOptimized(result, 0, len(result)-1)
	return result
}

func quickSortOptimized(arr []int, low, high int) {
	for low < high {
		if high-low < 10 {
			insertionSortRange(arr, low, high)
			return
		}

		pivotIndex := partitionMedianOfThree(arr, low, high)
		
		if pivotIndex-low < high-pivotIndex {
			quickSortOptimized(arr, low, pivotIndex-1)
			low = pivotIndex + 1
		} else {
			quickSortOptimized(arr, pivotIndex+1, high)
			high = pivotIndex - 1
		}
	}
}

func medianOfThree(arr []int, low, mid, high int) int {
	if arr[mid] < arr[low] {
		low, mid = mid, low
	}
	if arr[high] < arr[low] {
		low, high = high, low
	}
	if arr[high] < arr[mid] {
		mid, high = high, mid
	}
	return mid
}

func partitionMedianOfThree(arr []int, low, high int) int {
	mid := low + (high-low)/2
	medianIdx := medianOfThree(arr, low, mid, high)
	arr[medianIdx], arr[high] = arr[high], arr[medianIdx]
	
	pivot := arr[high]
	i := low - 1
	
	for j := low; j < high; j++ {
		if arr[j] <= pivot {
			i++
			arr[i], arr[j] = arr[j], arr[i]
		}
	}
	arr[i+1], arr[high] = arr[high], arr[i+1]
	return i + 1
}

func insertionSortRange(arr []int, low, high int) {
	for i := low + 1; i <= high; i++ {
		key := arr[i]
		j := i - 1
		for j >= low && arr[j] > key {
			arr[j+1] = arr[j]
			j--
		}
		arr[j+1] = key
	}
}

// 冒泡排序
func BubbleSort(arr []int) []int {
	n := len(arr)
	for i := 0; i < n-1; i++ {
		for j := 0; j < n-i-1; j++ {
			if arr[j] > arr[j+1] {
				arr[j], arr[j+1] = arr[j+1], arr[j]
			}
		}
	}
	return arr
}

// 选择排序
func SelectionSort(arr []int) []int {
	n := len(arr)
	for i := 0; i < n-1; i++ {
		minIndex := i
		for j := i + 1; j < n; j++ {
			if arr[j] < arr[minIndex] {
				minIndex = j
			}
		}
		arr[i], arr[minIndex] = arr[minIndex], arr[i]
	}
	return arr
}

// 插入排序
func InsertionSort(arr []int) []int {
	n := len(arr)
	for i := 1; i < n; i++ {
		key := arr[i]
		j := i - 1
		for j >= 0 && arr[j] > key {
			arr[j+1] = arr[j]
			j--
		}
		arr[j+1] = key
	}
	return arr
}
