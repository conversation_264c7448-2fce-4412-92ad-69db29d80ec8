package mysort

import (
	"reflect"
	"testing"
)

func TestQuickSort(t *testing.T) {
	tests := []struct {
		name     string
		input    []int
		expected []int
	}{
		{"empty array", []int{}, []int{}},
		{"single element", []int{5}, []int{5}},
		{"sorted array", []int{1, 2, 3, 4, 5}, []int{1, 2, 3, 4, 5}},
		{"reverse sorted", []int{5, 4, 3, 2, 1}, []int{1, 2, 3, 4, 5}},
		{"random array", []int{3, 6, 8, 10, 1, 2, 1}, []int{1, 1, 2, 3, 6, 8, 10}},
		{"duplicates", []int{5, 5, 5, 5}, []int{5, 5, 5, 5}},
		{"negative numbers", []int{-3, -1, -4, 2, 0}, []int{-4, -3, -1, 0, 2}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := QuickSort(append([]int(nil), tt.input...))
			if !reflect.DeepEqual(result, tt.expected) {
				if len(result) == 0 && len(tt.expected) == 0 {
					return
				}
				t.Errorf("QuickSort() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestBubbleSort(t *testing.T) {
	tests := []struct {
		name     string
		input    []int
		expected []int
	}{
		{"empty array", []int{}, []int{}},
		{"single element", []int{5}, []int{5}},
		{"sorted array", []int{1, 2, 3, 4, 5}, []int{1, 2, 3, 4, 5}},
		{"reverse sorted", []int{5, 4, 3, 2, 1}, []int{1, 2, 3, 4, 5}},
		{"random array", []int{3, 6, 8, 10, 1, 2, 1}, []int{1, 1, 2, 3, 6, 8, 10}},
		{"duplicates", []int{5, 5, 5, 5}, []int{5, 5, 5, 5}},
		{"negative numbers", []int{-3, -1, -4, 2, 0}, []int{-4, -3, -1, 0, 2}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BubbleSort(append([]int(nil), tt.input...))
			if !reflect.DeepEqual(result, tt.expected) {
				if len(result) == 0 && len(tt.expected) == 0 {
					return
				}
				t.Errorf("BubbleSort() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestSelectionSort(t *testing.T) {
	tests := []struct {
		name     string
		input    []int
		expected []int
	}{
		{"empty array", []int{}, []int{}},
		{"single element", []int{5}, []int{5}},
		{"sorted array", []int{1, 2, 3, 4, 5}, []int{1, 2, 3, 4, 5}},
		{"reverse sorted", []int{5, 4, 3, 2, 1}, []int{1, 2, 3, 4, 5}},
		{"random array", []int{3, 6, 8, 10, 1, 2, 1}, []int{1, 1, 2, 3, 6, 8, 10}},
		{"duplicates", []int{5, 5, 5, 5}, []int{5, 5, 5, 5}},
		{"negative numbers", []int{-3, -1, -4, 2, 0}, []int{-4, -3, -1, 0, 2}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SelectionSort(append([]int(nil), tt.input...))
			if !reflect.DeepEqual(result, tt.expected) {
				if len(result) == 0 && len(tt.expected) == 0 {
					return
				}
				t.Errorf("SelectionSort() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestInsertionSort(t *testing.T) {
	tests := []struct {
		name     string
		input    []int
		expected []int
	}{
		{"empty array", []int{}, []int{}},
		{"single element", []int{5}, []int{5}},
		{"sorted array", []int{1, 2, 3, 4, 5}, []int{1, 2, 3, 4, 5}},
		{"reverse sorted", []int{5, 4, 3, 2, 1}, []int{1, 2, 3, 4, 5}},
		{"random array", []int{3, 6, 8, 10, 1, 2, 1}, []int{1, 1, 2, 3, 6, 8, 10}},
		{"duplicates", []int{5, 5, 5, 5}, []int{5, 5, 5, 5}},
		{"negative numbers", []int{-3, -1, -4, 2, 0}, []int{-4, -3, -1, 0, 2}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := InsertionSort(append([]int(nil), tt.input...))
			if !reflect.DeepEqual(result, tt.expected) {
				if len(result) == 0 && len(tt.expected) == 0 {
					return
				}
				t.Errorf("InsertionSort() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func BenchmarkQuickSort(b *testing.B) {
	data := []int{64, 34, 25, 12, 22, 11, 90, 5, 77, 30, 8, 12, 45, 39, 88, 76, 50, 2}
	for i := 0; i < b.N; i++ {
		QuickSort(append([]int(nil), data...))
	}
}

func BenchmarkQuickSortLarge(b *testing.B) {
	data := make([]int, 10000)
	for i := 0; i < len(data); i++ {
		data[i] = len(data) - i
	}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		QuickSort(append([]int(nil), data...))
	}
}

func BenchmarkQuickSortWorstCase(b *testing.B) {
	data := make([]int, 1000)
	for i := 0; i < len(data); i++ {
		data[i] = i
	}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		QuickSort(append([]int(nil), data...))
	}
}

func BenchmarkBubbleSort(b *testing.B) {
	data := []int{64, 34, 25, 12, 22, 11, 90, 5, 77, 30, 8, 12, 45, 39, 88, 76, 50, 2}
	for i := 0; i < b.N; i++ {
		BubbleSort(append([]int(nil), data...))
	}
}

func BenchmarkSelectionSort(b *testing.B) {
	data := []int{64, 34, 25, 12, 22, 11, 90, 5, 77, 30, 8, 12, 45, 39, 88, 76, 50, 2}
	for i := 0; i < b.N; i++ {
		SelectionSort(append([]int(nil), data...))
	}
}

func BenchmarkInsertionSort(b *testing.B) {
	data := []int{64, 34, 25, 12, 22, 11, 90, 5, 77, 30, 8, 12, 45, 39, 88, 76, 50, 2}
	for i := 0; i < b.N; i++ {
		InsertionSort(append([]int(nil), data...))
	}
}