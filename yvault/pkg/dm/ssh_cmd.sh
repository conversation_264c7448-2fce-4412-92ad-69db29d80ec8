#!/bin/sh

authorized_key_dir=/.ssh

#
# Insert a string into the device
#
add_oper()
{
    add_params=$@

    line_num=$(grep -n "$add_params" ${authorized_key_dir}/authorized_keys2 | cut -d: -f1)
    if [ -n "$line_num" ]; then
        echo "existing permissions"
    else
        echo "" >> ${authorized_key_dir}/authorized_keys2
        echo "$add_params" >> ${authorized_key_dir}/authorized_keys2
        chmod 600 ${authorized_key_dir}/authorized_keys2 2>/dev/null
        echo "push succeeded"
    fi
}

#
# Delete a string from file
#
del_oper()
{
    del_param=$@

    line_num=$(grep -n "$del_param" ${authorized_key_dir}/authorized_keys2 | cut -d: -f1)
    if [ -n "$line_num" ]; then
        sed -i "${line_num}d" ${authorized_key_dir}/authorized_keys2
        chmod 600 ${authorized_key_dir}/authorized_keys2 2>/dev/null
        echo "delete succeeded"
    else
        echo "non-existent permissions"
    fi
}

#
# execute cmd func
#
cmd()
{
    command=$1
    shift 1
    param=$@

    case "$command" in
    # add ssh public key to authorized_keys2
    add)
        add_oper $param
        ;;
    # del ssh public key from authorized_keys2
    del)
        del_oper $param
        ;;
    *)
        echo "Access denied"
        exit
        ;;
    esac
}

#
# main
#
cmd $@