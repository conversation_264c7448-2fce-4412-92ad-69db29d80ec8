package dm

import (
	"bytes"
	"errors"
	"fmt"
	"path/filepath"
	"strings"

	"golang.org/x/crypto/ssh"
)

var ErrAuthorizedKeyNotFound error = errors.New("authorized key not found")
var ErrAuthorizedKeyAlreadyExists error = errors.New("authorized key already exists")

// ManagerConfig 保存 DeviceManager 的配置
type ManagerConfig struct {
	Host        string
	Port        int
	User        string
	AuthMethods []ssh.AuthMethod

	AuthorizedKeysPath string
}

// DeviceManager 管理远程服务器上的授权密钥
type DeviceManager struct {
	client             *ssh.Client
	authorizedKeysPath string
}

// NewDeviceManager 创建一个新的 DeviceManager
func NewDeviceManager(cfg *ManagerConfig) (*DeviceManager, error) {

	config := &ssh.ClientConfig{
		User:            cfg.User,
		Auth:            cfg.AuthMethods,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 在实际应用中，应使用适当的主机密钥回调
	}

	addr := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)
	client, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", addr, err)
	}

	return &DeviceManager{
		client:             client,
		authorizedKeysPath: cfg.AuthorizedKeysPath,
	}, nil
}

// Close 关闭底层 SSH 客户端连接和 PKCS#11 context
func (m *DeviceManager) Close() {
	if m.client != nil {
		m.client.Close()
	}
}

func (m *DeviceManager) runCommand(cmd string) (string, error) {
	session, err := m.client.NewSession()
	if err != nil {
		return "", fmt.Errorf("failed to create session: %v", err)
	}
	defer session.Close()

	var stdoutBuf bytes.Buffer
	session.Stdout = &stdoutBuf
	err = session.Run(cmd)
	if err != nil {
		return "", fmt.Errorf("failed to run command '%s': %v", cmd, err)
	}
	return stdoutBuf.String(), nil
}

// ListKeys 列出授权的公钥
func (m *DeviceManager) ListKeys() ([]string, error) {
	// 使用安全的文件读取方法
	out, err := m.safeReadFile(m.authorizedKeysPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read authorized keys file: %w", err)
	}

	// 如果文件为空，返回空列表
	if out == "" {
		return []string{}, nil
	}

	keys := strings.Split(strings.TrimSpace(out), "\n")
	var filteredKeys []string
	for _, k := range keys {
		if strings.TrimSpace(k) != "" {
			filteredKeys = append(filteredKeys, k)
		}
	}
	return filteredKeys, nil
}

// AddKey 向 authorized_keys 文件添加公钥
func (m *DeviceManager) AddKey(key string) error {

	if err := validateSSHPublicKey(key); err != nil {
		return fmt.Errorf("invalid SSH public key: %w", err)
	}

	key = strings.TrimSpace(key)
	// 检查密钥是否已存在
	keys, err := m.ListKeys()
	if err != nil {
		return err
	}
	for _, existingKey := range keys {
		if strings.TrimSpace(existingKey) == key {
			return ErrAuthorizedKeyAlreadyExists
		}
	}

	// 使用安全的文件追加方法
	err = m.safeAppendFile(m.authorizedKeysPath, key+"\n\n")
	if err != nil {
		return fmt.Errorf("failed to add key: %w", err)
	}

	// 确保正确的权限
	_, err = m.runCommand(fmt.Sprintf("chmod 600 %q", m.authorizedKeysPath))
	if err != nil {
		return fmt.Errorf("failed to set permissions: %v", err)
	}

	return nil
}

// DeleteKey 从 authorized_keys 文件中删除公钥
func (m *DeviceManager) DeleteKey(keyToDelete string) error {
	if err := validateSSHPublicKey(keyToDelete); err != nil {
		return fmt.Errorf("invalid SSH public key: %w", err)
	}

	keyToDelete = strings.TrimSpace(keyToDelete)
	keys, err := m.ListKeys()
	if err != nil {
		return err
	}

	var newKeys []string
	var found bool
	for _, k := range keys {
		if strings.TrimSpace(k) != keyToDelete {
			newKeys = append(newKeys, k)
		} else {
			found = true
		}
	}

	if !found {
		return ErrAuthorizedKeyNotFound
	}

	if len(newKeys) == 0 {
		// 如果没有剩余密钥，删除文件
		err = m.safeDeleteFile(m.authorizedKeysPath)
		if err != nil {
			return fmt.Errorf("failed to delete empty file: %w", err)
		}
	} else {
		// 将新的密钥集合写入临时文件，然后原子性移动
		newKeysContent := strings.Join(newKeys, "\n\n") + "\n\n"
		err = m.safeWriteFile(m.authorizedKeysPath, newKeysContent, "0600")
		if err != nil {
			return fmt.Errorf("failed to write updated keys: %w", err)
		}
	}

	return nil
}

// 安全的文件删除
func (m *DeviceManager) safeDeleteFile(remotePath string) error {
	if err := validatePath(remotePath); err != nil {
		return fmt.Errorf("invalid path: %w", err)
	}

	// 使用 %q 进行安全引用删除文件
	cmd := fmt.Sprintf("rm %q", remotePath)
	out, err := m.runCommand(cmd)
	if err != nil {
		// 如果文件不存在，不算错误
		if strings.Contains(err.Error(), "No such file or directory") {
			return nil
		}
		return fmt.Errorf("failed to delete file %q: %v, %s", remotePath, err, out)
	}

	return nil
}

// 安全的文件读取
func (m *DeviceManager) safeReadFile(remotePath string) (string, error) {
	if err := validatePath(remotePath); err != nil {
		return "", fmt.Errorf("invalid path: %w", err)
	}

	// 使用 %q 进行安全引用
	cmd := fmt.Sprintf("cat %q", remotePath)
	out, err := m.runCommand(cmd)
	if err != nil {
		if strings.Contains(err.Error(), "No such file or directory") {
			return "", nil
		}
		return "", fmt.Errorf("failed to read file %q: %v, %s", remotePath, err, out)
	}

	return out, nil
}

// 安全的文件写入
func (m *DeviceManager) safeWriteFile(remotePath, content string, mode string) error {
	if err := validatePath(remotePath); err != nil {
		return fmt.Errorf("invalid path: %w", err)
	}

	// 创建目录
	dir := filepath.Dir(remotePath)
	if dir != "." {
		cmd := fmt.Sprintf("mkdir -p %q", dir)
		_, err := m.runCommand(cmd)
		if err != nil {
			return fmt.Errorf("failed to create directory %q: %v", dir, err)
		}
	}

	// 使用临时文件和 here document
	tempPath := remotePath + ".tmp"
	cmd := fmt.Sprintf("cat > %q << 'EOF'\n%s\nEOF", tempPath, content)
	_, err := m.runCommand(cmd)
	if err != nil {
		return fmt.Errorf("failed to write to temporary file %q: %v", tempPath, err)
	}

	// 设置权限
	cmd = fmt.Sprintf("chmod %s %q", mode, tempPath)
	_, err = m.runCommand(cmd)
	if err != nil {
		return fmt.Errorf("failed to set permissions on %q: %v", tempPath, err)
	}

	// 原子性移动
	cmd = fmt.Sprintf("mv %q %q", tempPath, remotePath)
	_, err = m.runCommand(cmd)
	if err != nil {
		return fmt.Errorf("failed to move %q to %q: %v", tempPath, remotePath, err)
	}

	return nil
}

// 安全的文件追加
func (m *DeviceManager) safeAppendFile(remotePath, content string) error {

	if err := validatePath(remotePath); err != nil {
		return fmt.Errorf("invalid path: %w", err)
	}

	// 创建目录
	dir := filepath.Dir(remotePath)
	if dir != "." {
		cmd := fmt.Sprintf("mkdir -p %q", dir)
		_, err := m.runCommand(cmd)
		if err != nil {
			return fmt.Errorf("failed to create directory %q: %v", dir, err)
		}
	}

	// 使用 here document 安全追加
	cmd := fmt.Sprintf("cat >> %q << 'EOF'\n%s\nEOF", remotePath, content)
	_, err := m.runCommand(cmd)
	if err != nil {
		return fmt.Errorf("failed to append to file %q: %v", remotePath, err)
	}

	return nil
}

// 验证公钥格式
func validateSSHPublicKey(key string) error {
	if key == "" {
		return fmt.Errorf("key cannot be empty")
	}

	// 检查是否包含危险字符
	dangerousChars := []string{"'", "\"", "`", "$", "(", ")", "&", "|", ";", "\\"}
	for _, char := range dangerousChars {
		if strings.Contains(key, char) {
			return fmt.Errorf("key contains dangerous character: %s", char)
		}
	}

	// 验证 SSH 公钥格式
	parts := strings.Fields(key)
	if len(parts) < 2 {
		return fmt.Errorf("invalid SSH public key format")
	}

	return nil
}

// 安全的路径验证
func validatePath(path string) error {
	if path == "" {
		return fmt.Errorf("path cannot be empty")
	}

	// 检查路径遍历攻击
	if strings.Contains(path, "..") || strings.Contains(path, "//") {
		return fmt.Errorf("path contains invalid characters")
	}

	// 限制路径长度
	if len(path) > 1024 {
		return fmt.Errorf("path too long")
	}

	return nil
}

// 获取设备变量的通用方法
func (m *DeviceManager) getDeviceVar(varName string) (string, error) {
	// 先判断 /dev/block/mmcblk0p2 是否存在
	checkCmd := "test -e /dev/block/mmcblk0p2 && echo exist || echo notexist"
	out, err := m.runCommand(checkCmd)
	if err != nil {
		return "", fmt.Errorf("failed to check mmcblk0p2: %v", err)
	}
	out = strings.TrimSpace(out)

	var cmds []string
	if out == "exist" {
		cmds = []string{
			fmt.Sprintf("userenv -d /dev/block/mmcblk0p2 -g %s", varName),
			fmt.Sprintf("userenv -d sysenv -g %s", varName),
		}
	} else {
		cmds = []string{
			fmt.Sprintf("userenv -d /dev/mtd2 -g %s", varName),
		}
	}

	// 依次尝试命令
	for _, cmd := range cmds {
		res, err := m.runCommand(cmd)
		if err != nil {
			continue
		}
		res = strings.TrimSpace(res)
		if res == "" {
			continue
		}
		// 解析 key=value
		parts := strings.SplitN(res, "=", 2)
		if len(parts) == 2 {
			return strings.TrimSpace(parts[1]), nil
		}
	}
	return "", fmt.Errorf("failed to get variable %s", varName)
}

// 获取设备 SN
func (m *DeviceManager) GetDeviceSN() (string, error) {
	return m.getDeviceVar("SN_YL")
}

// 获取设备 MAC
func (m *DeviceManager) GetDeviceMAC() (string, error) {
	return m.getDeviceVar("ethaddr")
}
