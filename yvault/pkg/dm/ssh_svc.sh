#!/bin/sh
############################# func #############################
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/phone/lib64:/boot/lib64:/phone/lib:/boot/lib
export PATH=$PATH:/phone/bin:/boot/bin

ssh_conf_dir=/
ssh_save_dir=/phone/security/ssh
sys_mode=`/system/bin/getprop ro.sys.mode`

set_pubkey_to_authkey()
{
    key_flags=$@
    key_line=$(grep -n "${key_flags}" ${ssh_save_dir}/authorized_keys | cut -d: -f1)

    if [ -n "${key_line}" ]; then
        key_line=$(expr $key_line + 1)
        echo "${key_flags}" >> ${ssh_conf_dir}/.ssh/authorized_keys
        sed -n "${key_line}p" ${ssh_save_dir}/authorized_keys >> ${ssh_conf_dir}/.ssh/authorized_keys
        echo "" >> ${ssh_conf_dir}/.ssh/authorized_keys
    fi
}

boot_ssh_sync_rsa()
{
    ## file & dir permissions check
    chmod 0600 ${ssh_conf_dir}/.ssh/*

    chown root:root ${ssh_conf_dir}/.ssh

    # restore SE file tag for android
    restorecon -RF ${ssh_conf_dir}/.ssh
}

ssh_config_init()
{
    is_master=0
    is_slaved=0
    ## If it has already been initialized once, exit
    ## fix bug: https://worklink.yealink.com/issues/bug/1310586
    if [ -f ${ssh_conf_dir}/.ssh/config ] && [ ! -z "$(grep -n "Init_ok=1" /var/run/sshd)" ]; then
        return
    fi

    ##  get device mode by ro.sys.mode
    if [ -z "${sys_mode}" ] || [ "${sys_mode}" == "0" ]; then
        is_master=1
    else
        is_slaved=1
    fi

    ## master + slave mode
    if [ "${is_master}" != "0" ] && [ "${is_slaved}" != "0" ]; then
        cp /phone/security/ssh/authorized_keys ${ssh_conf_dir}/.ssh/
    ## master mode
    elif [ "${is_master}" != "0" ]; then
        rm ${ssh_conf_dir}/.ssh/authorized_keys
        set_pubkey_to_authkey "## ca-rd key"
    ## slave mode
    elif [ "${is_slaved}" != "0" ]; then
        cp /phone/security/ssh/authorized_keys ${ssh_conf_dir}/.ssh/
    ## unknown mode
    else
        echo "The device is in unknown mode"
    fi
    ## if have the config file, must copy it.
    if [ -f "/phone/security/ssh/config" ]; then
        cp /phone/security/ssh/config ${ssh_conf_dir}/.ssh/
    fi
}

boot_ssh_init()
{
    if [ ! -d /tmp/log ];then
        mkdir -p /tmp/log
        chmod 777 /tmp/log
    fi

    if [ ! -d /config/.ssh ];then
        mkdir -p /config/.ssh
        chmod 644 /config/.ssh
    fi

    insmod_ktpm=`lsmod | grep ktpm`
    if [ -f /phone/boot/driver/ktpm.ko ] && [ -z "${insmod_ktpm}" ]; then
        echo "insmod HSM-ktpm"
        insmod /phone/boot/driver/ktpm.ko
    fi

    ## mount the config dir to default dir
    if [ -d /.ssh ];then
        ## Do not mount again
        ## fix bug: https://worklink.yealink.com/issues/bug/1311680
        if [ -z "$(grep -n "Init_ok=1" /var/run/sshd)" ]; then
            mount --bind /config/.ssh /.ssh
        fi
        chmod 644 /.ssh
        ssh_config_init
        boot_ssh_sync_rsa
    fi

    ## remove the known_host, To avoid being inaccessible due to "REMOTE HOST IDENTIFICATION HAS CHANGED!"
    if  [ -f /.ssh/known_hosts ];then
        rm /.ssh/known_hosts
    fi

    echo "Init_ok=1" > /var/run/sshd
    chmod 0666 /var/run/sshd
}

boot_ssh_run()
{
    while true
    do
        cfg_ready=`getprop net.cfgserver.state`
        cfg_ready_edla=`getprop vendor.net.cfgserver.state`
        cfg_ready_translate=`getprop net.cfgserver.start_translate`
        if [ $cfg_ready == "ready" ] || [ $cfg_ready_edla == "ready" ] || [ $cfg_ready_translate == "true" ];then
            echo "ssh_svc:cfgserver is already ready" >/dev/console 2>&1
            break
        fi
        sleep 1
    done
    sshd_enable=`/boot/bin/cfgserver get features.onking 0`
    dev_is_master=0
    if [ -z "${sys_mode}" ] || [ "${sys_mode}" == "0" ]; then
        dev_is_master=1
    fi
    if [ "$sshd_enable" != "1" ] && [ "${dev_is_master}" == "1" ]; then
        echo "---------------disable sshd------------------------"
        return
    fi

    if [ -x /phone/bin/sshd ];then
        echo "=====need start sshd====="
        if [ $sys_mode -eq 1 ];then
            echo "=====sshd ns start====="
            start sshdns
        else
            echo "=====sshd start====="
            start sshd
        fi
    fi
}

############################# run main #############################
echo "---------------start ssh------------------------"
boot_ssh_init
boot_ssh_run
echo "---------------finish ssh------------------------"