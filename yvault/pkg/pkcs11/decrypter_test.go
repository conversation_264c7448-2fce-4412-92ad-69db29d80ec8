package pkcs11

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"testing"
)

func TestNewPKCS11Decrypter(t *testing.T) {
	client := &Client{}

	tests := []struct {
		name    string
		keyPair *KeyPair
		wantErr bool
	}{
		{
			name: "valid RSA key",
			keyPair: &KeyPair{
				Handle:  1000,
				Label:   "test-rsa-key",
				KeyType: KeyPairTypeRSA,
			},
			wantErr: false,
		},
		{
			name: "invalid ECDSA key",
			keyPair: &KeyPair{
				Handle:  1000,
				Label:   "test-ecdsa-key",
				KeyType: KeyPairTypeECDSA,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			decrypter, err := NewPKCS11Decrypter(client, tt.keyPair)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("NewPKCS11Decrypter() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil {
				if decrypter == nil {
					t.Error("Expected decrypter to be created")
				}
				if decrypter.client != client {
					t.Error("Expected decrypter to have the correct client")
				}
				if decrypter.keyPair != tt.keyPair {
					t.Error("Expected decrypter to have the correct keyPair")
				}
			}
		})
	}
}

func TestPKCS11Decrypter_Public(t *testing.T) {
	rsaKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	keyPair := &KeyPair{
		Handle:    1000,
		Label:     "test-key",
		ID:        []byte("test-id"),
		KeyType:   KeyPairTypeRSA,
		KeySize:   2048,
		PublicKey: &rsaKey.PublicKey,
	}

	decrypter, err := NewPKCS11Decrypter(&Client{}, keyPair)
	if err != nil {
		t.Fatalf("Failed to create decrypter: %v", err)
	}

	publicKey := decrypter.Public()
	if publicKey != keyPair.PublicKey {
		t.Error("Expected Public() to return the keyPair's public key")
	}

	// Verify it's the correct RSA public key
	rsaPub, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		t.Fatal("Expected RSA public key")
	}
	if rsaPub.N.Cmp(rsaKey.PublicKey.N) != 0 {
		t.Error("Public key modulus doesn't match")
	}
	if rsaPub.E != rsaKey.PublicKey.E {
		t.Error("Public key exponent doesn't match")
	}
}

func TestDecryptionMechanismSelection(t *testing.T) {
	// Test the logic for selecting decryption mechanisms
	tests := []struct {
		name         string
		opts         crypto.DecrypterOpts
		expectedMech uint
		wantErr      bool
	}{
		{
			name:         "nil options (default PKCS#1 v1.5)",
			opts:         nil,
			expectedMech: 0x00000001, // CKM_RSA_PKCS
			wantErr:      false,
		},
		{
			name:         "PKCS#1 v1.5 explicit",
			opts:         &rsa.PKCS1v15DecryptOptions{},
			expectedMech: 0x00000001, // CKM_RSA_PKCS
			wantErr:      false,
		},
		{
			name: "OAEP with SHA-1",
			opts: &rsa.OAEPOptions{
				Hash:  crypto.SHA1,
				Label: nil,
			},
			expectedMech: 0x00000009, // CKM_RSA_PKCS_OAEP
			wantErr:      false,
		},
		{
			name: "OAEP with SHA-256",
			opts: &rsa.OAEPOptions{
				Hash:  crypto.SHA256,
				Label: nil,
			},
			expectedMech: 0x00000009, // CKM_RSA_PKCS_OAEP
			wantErr:      false,
		},
		{
			name: "OAEP with unsupported hash",
			opts: &rsa.OAEPOptions{
				Hash:  crypto.SHA512,
				Label: nil,
			},
			wantErr: true,
		},
		{
			name: "OAEP with label",
			opts: &rsa.OAEPOptions{
				Hash:  crypto.SHA256,
				Label: []byte("test-label"),
			},
			expectedMech: 0x00000009, // CKM_RSA_PKCS_OAEP
			wantErr:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test the mechanism selection logic
			var mechanismType uint
			var hasOAEPParams bool

			if tt.opts == nil {
				mechanismType = 0x00000001 // CKM_RSA_PKCS
			} else {
				switch opt := tt.opts.(type) {
				case *rsa.PKCS1v15DecryptOptions:
					mechanismType = 0x00000001 // CKM_RSA_PKCS
				case *rsa.OAEPOptions:
					switch opt.Hash {
					case crypto.SHA1:
						mechanismType = 0x00000009 // CKM_RSA_PKCS_OAEP
						hasOAEPParams = true
					case crypto.SHA256:
						mechanismType = 0x00000009 // CKM_RSA_PKCS_OAEP
						hasOAEPParams = true
					default:
						// Unsupported hash
						if !tt.wantErr {
							t.Error("Expected error for unsupported hash")
						}
						return
					}
				default:
					if !tt.wantErr {
						t.Error("Expected error for unsupported options")
					}
					return
				}
			}

			if mechanismType != tt.expectedMech {
				t.Errorf("Expected mechanism 0x%08X, got 0x%08X", tt.expectedMech, mechanismType)
			}

			// Test OAEP parameter handling
			if hasOAEPParams {
				if oaepOpts, ok := tt.opts.(*rsa.OAEPOptions); ok {
					// Test label handling
					if len(oaepOpts.Label) > 0 {
						// Should set SourceData to the label
						if string(oaepOpts.Label) != "test-label" {
							t.Error("Expected label to be preserved")
						}
					}
				}
			}
		})
	}
}

func TestOAEPParameterGeneration(t *testing.T) {
	tests := []struct {
		name     string
		hash     crypto.Hash
		expected map[string]uint
		wantErr  bool
	}{
		{
			name: "SHA-1",
			hash: crypto.SHA1,
			expected: map[string]uint{
				"HashAlg":    0x00000220, // CKM_SHA_1
				"MGF":        0x00000001, // CKG_MGF1_SHA1
				"SourceType": 0x00000001, // CKZ_DATA_SPECIFIED
			},
			wantErr: false,
		},
		{
			name: "SHA-256",
			hash: crypto.SHA256,
			expected: map[string]uint{
				"HashAlg":    0x00000250, // CKM_SHA256
				"MGF":        0x00000002, // CKG_MGF1_SHA256
				"SourceType": 0x00000001, // CKZ_DATA_SPECIFIED
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test OAEP parameter creation logic
			var hashAlg, mgf, sourceType uint

			switch tt.hash {
			case crypto.SHA1:
				hashAlg = 0x00000220 // CKM_SHA_1
				mgf = 0x00000001     // CKG_MGF1_SHA1
				sourceType = 0x00000001 // CKZ_DATA_SPECIFIED
			case crypto.SHA256:
				hashAlg = 0x00000250 // CKM_SHA256
				mgf = 0x00000002     // CKG_MGF1_SHA256
				sourceType = 0x00000001 // CKZ_DATA_SPECIFIED
			default:
				if !tt.wantErr {
					t.Error("Expected error for unsupported hash")
				}
				return
			}

			if hashAlg != tt.expected["HashAlg"] {
				t.Errorf("Expected HashAlg 0x%08X, got 0x%08X", tt.expected["HashAlg"], hashAlg)
			}
			if mgf != tt.expected["MGF"] {
				t.Errorf("Expected MGF 0x%08X, got 0x%08X", tt.expected["MGF"], mgf)
			}
			if sourceType != tt.expected["SourceType"] {
				t.Errorf("Expected SourceType 0x%08X, got 0x%08X", tt.expected["SourceType"], sourceType)
			}
		})
	}
}

func TestNewRSADecrypter(t *testing.T) {
	client := &Client{}
	keyPair := &KeyPair{
		Handle:  1000,
		Label:   "test-rsa-key",
		KeyType: KeyPairTypeRSA,
	}

	rsaDecrypter, err := NewRSADecrypter(client, keyPair)
	if err != nil {
		t.Fatalf("Failed to create RSA decrypter: %v", err)
	}

	if rsaDecrypter == nil {
		t.Fatal("Expected RSA decrypter to be created")
	}
	if rsaDecrypter.PKCS11Decrypter == nil {
		t.Error("Expected RSA decrypter to have a PKCS11Decrypter")
	}
}

func TestRSADecrypter_ConvenienceMethods(t *testing.T) {
	// Test the convenience method signatures
	// These would normally call the underlying Decrypt method

	client := &Client{}
	keyPair := &KeyPair{
		Handle:  1000,
		Label:   "test-rsa-key",
		KeyType: KeyPairTypeRSA,
	}

	rsaDecrypter, err := NewRSADecrypter(client, keyPair)
	if err != nil {
		t.Fatalf("Failed to create RSA decrypter: %v", err)
	}

	// Test method signatures exist
	testData := []byte("test data")

	// These methods would normally interact with PKCS#11, but we're just testing
	// that the interface is correct
	t.Run("DecryptPKCS1v15 signature", func(t *testing.T) {
		// Test that the method exists and has the right signature
		_, err := rsaDecrypter.DecryptPKCS1v15(testData)
		// We expect this to fail since we don't have a real PKCS#11 connection
		if err == nil {
			t.Log("Unexpected success - this would require a real PKCS#11 connection")
		}
	})

	t.Run("DecryptOAEP signature", func(t *testing.T) {
		// Test that the method exists and has the right signature
		_, err := rsaDecrypter.DecryptOAEP(crypto.SHA256, testData, []byte("label"))
		// We expect this to fail since we don't have a real PKCS#11 connection
		if err == nil {
			t.Log("Unexpected success - this would require a real PKCS#11 connection")
		}
	})
}

// Test interface compliance
func TestPKCS11Decrypter_ImplementsCryptoDecrypter(t *testing.T) {
	var _ crypto.Decrypter = &PKCS11Decrypter{}
}

// Test label handling edge cases
func TestOAEPLabelHandling(t *testing.T) {
	tests := []struct {
		name  string
		label []byte
		valid bool
	}{
		{
			name:  "nil label",
			label: nil,
			valid: true,
		},
		{
			name:  "empty label",
			label: []byte{},
			valid: true,
		},
		{
			name:  "non-empty label",
			label: []byte("test-label"),
			valid: true,
		},
		{
			name:  "long label",
			label: make([]byte, 1000),
			valid: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test label validation logic
			// In the real implementation, this would be passed to PKCS#11
			
			// The PKCS#11 standard allows empty or non-empty labels
			// Length validation would be done by the PKCS#11 library
			if len(tt.label) > 0 {
				// Non-empty label should be preserved
				if string(tt.label) == "test-label" {
					// Expected behavior
				}
			} else {
				// Empty or nil label is also valid
			}

			// All test cases should be valid according to PKCS#11 standard
			if !tt.valid {
				t.Error("All labels should be valid in PKCS#11")
			}
		})
	}
}

// Test decryption data validation
func TestDecryptionDataValidation(t *testing.T) {
	tests := []struct {
		name       string
		ciphertext []byte
		keySize    int
		valid      bool
	}{
		{
			name:       "valid 2048-bit RSA ciphertext",
			ciphertext: make([]byte, 256), // 2048 bits = 256 bytes
			keySize:    2048,
			valid:      true,
		},
		{
			name:       "valid 4096-bit RSA ciphertext",
			ciphertext: make([]byte, 512), // 4096 bits = 512 bytes
			keySize:    4096,
			valid:      true,
		},
		{
			name:       "empty ciphertext",
			ciphertext: []byte{},
			keySize:    2048,
			valid:      false,
		},
		{
			name:       "too short ciphertext",
			ciphertext: make([]byte, 100),
			keySize:    2048,
			valid:      false,
		},
		{
			name:       "too long ciphertext",
			ciphertext: make([]byte, 1000),
			keySize:    2048,
			valid:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test ciphertext length validation
			expectedLen := tt.keySize / 8 // Convert bits to bytes
			actualLen := len(tt.ciphertext)

			isValidLength := actualLen == expectedLen
			
			if isValidLength != tt.valid {
				t.Errorf("Expected validation result %v, got %v (ciphertext len: %d, expected: %d)", 
					tt.valid, isValidLength, actualLen, expectedLen)
			}
		})
	}
}

// Mock client for testing basic decryption flow
type MockDecryptClient struct {
	sessionHandle uint
	connected     bool
	shouldFail    bool
}

func (m *MockDecryptClient) GetSession() (uint, error) {
	if !m.connected {
		return 0, NewPKCS11Error(ErrNotInitialized, "not connected", nil)
	}
	if m.shouldFail {
		return 0, NewPKCS11Error(ErrOperationFailed, "operation failed", nil)
	}
	return m.sessionHandle, nil
}

func TestBasicDecryptionFlow_WithMockClient(t *testing.T) {
	mockClient := &MockDecryptClient{
		sessionHandle: 456,
		connected:     true,
		shouldFail:    false,
	}

	// Test successful session retrieval
	session, err := mockClient.GetSession()
	if err != nil {
		t.Errorf("Expected to get session, got error: %v", err)
	}
	if session != 456 {
		t.Errorf("Expected session 456, got %d", session)
	}

	// Test connection error
	mockClient.connected = false
	_, err = mockClient.GetSession()
	if err == nil {
		t.Error("Expected error when not connected")
	}
	if !IsPKCS11Error(err, ErrNotInitialized) {
		t.Error("Expected ErrNotInitialized error")
	}

	// Test operation failure
	mockClient.connected = true
	mockClient.shouldFail = true
	_, err = mockClient.GetSession()
	if err == nil {
		t.Error("Expected error when operation should fail")
	}
	if !IsPKCS11Error(err, ErrOperationFailed) {
		t.Error("Expected ErrOperationFailed error")
	}
}