package pkcs11

import (
	"os"
	"testing"
)

func TestNewConfig(t *testing.T) {
	config := NewConfig("/path/to/lib.so", 1, "1234")
	
	if config.LibraryPath != "/path/to/lib.so" {
		t.<PERSON><PERSON><PERSON>("Expected LibraryPath '/path/to/lib.so', got '%s'", config.LibraryPath)
	}
	if config.SlotID != 1 {
		t.<PERSON><PERSON><PERSON>("Expected SlotID 1, got %d", config.SlotID)
	}
	if config.UserPIN != "1234" {
		t.<PERSON><PERSON>("Expected UserPIN '1234', got '%s'", config.UserPIN)
	}
}

func TestConfigValidate(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
	}{
		{
			name: "valid config with temp library",
			config: &Config{
				LibraryPath: "/tmp/libpkcs11.so",
				SlotID:      0,
				UserPIN:     "1234",
			},
			wantErr: false,
		},
		{
			name: "empty library path",
			config: &Config{
				LibraryPath: "",
				SlotID:      0,
				UserPIN:     "1234",
			},
			wantErr: true,
		},
		{
			name: "empty user PIN",
			config: &Config{
				LibraryPath: "/tmp/libpkcs11.so",
				SlotID:      0,
				UserPIN:     "",
			},
			wantErr: true,
		},
		{
			name: "non-existent library path",
			config: &Config{
				LibraryPath: "/non/existent/path/lib.so",
				SlotID:      0,
				UserPIN:     "1234",
			},
			wantErr: true,
		},
	}

	// Create a temporary library file for testing
	tmpFile, err := os.CreateTemp("", "libpkcs11*.so")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())
	tmpFile.Close()

	// Update the valid test case to use the actual temp file
	tests[0].config.LibraryPath = tmpFile.Name()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Config.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewConfigFromEnv(t *testing.T) {
	// Save original environment
	originalLibPath := os.Getenv("PKCS11_LIBRARY_PATH")
	originalSlotID := os.Getenv("PKCS11_SLOT_ID")
	originalUserPIN := os.Getenv("PKCS11_USER_PIN")

	// Clean up after test
	defer func() {
		if originalLibPath != "" {
			os.Setenv("PKCS11_LIBRARY_PATH", originalLibPath)
		} else {
			os.Unsetenv("PKCS11_LIBRARY_PATH")
		}
		if originalSlotID != "" {
			os.Setenv("PKCS11_SLOT_ID", originalSlotID)
		} else {
			os.Unsetenv("PKCS11_SLOT_ID")
		}
		if originalUserPIN != "" {
			os.Setenv("PKCS11_USER_PIN", originalUserPIN)
		} else {
			os.Unsetenv("PKCS11_USER_PIN")
		}
	}()

	tests := []struct {
		name        string
		envVars     map[string]string
		expected    *Config
		wantErr     bool
	}{
		{
			name: "all environment variables set",
			envVars: map[string]string{
				"PKCS11_LIBRARY_PATH": "/custom/path/lib.so",
				"PKCS11_SLOT_ID":      "2",
				"PKCS11_USER_PIN":     "secret",
			},
			expected: &Config{
				LibraryPath: "/custom/path/lib.so",
				SlotID:      2,
				UserPIN:     "secret",
			},
			wantErr: false,
		},
		{
			name: "default values with required PIN",
			envVars: map[string]string{
				"PKCS11_USER_PIN": "secret",
			},
			expected: &Config{
				LibraryPath: "/usr/lib/pkcs11/libpkcs11.so",
				SlotID:      0,
				UserPIN:     "secret",
			},
			wantErr: false,
		},
		{
			name: "missing user PIN",
			envVars: map[string]string{
				"PKCS11_LIBRARY_PATH": "/path/lib.so",
				"PKCS11_SLOT_ID":      "1",
			},
			wantErr: true,
		},
		{
			name: "invalid slot ID",
			envVars: map[string]string{
				"PKCS11_SLOT_ID":  "invalid",
				"PKCS11_USER_PIN": "secret",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clear environment
			os.Unsetenv("PKCS11_LIBRARY_PATH")
			os.Unsetenv("PKCS11_SLOT_ID")
			os.Unsetenv("PKCS11_USER_PIN")

			// Set test environment variables
			for key, value := range tt.envVars {
				os.Setenv(key, value)
			}

			config, err := NewConfigFromEnv()
			if (err != nil) != tt.wantErr {
				t.Errorf("NewConfigFromEnv() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil && tt.expected != nil {
				if config.LibraryPath != tt.expected.LibraryPath {
					t.Errorf("Expected LibraryPath '%s', got '%s'", tt.expected.LibraryPath, config.LibraryPath)
				}
				if config.SlotID != tt.expected.SlotID {
					t.Errorf("Expected SlotID %d, got %d", tt.expected.SlotID, config.SlotID)
				}
				if config.UserPIN != tt.expected.UserPIN {
					t.Errorf("Expected UserPIN '%s', got '%s'", tt.expected.UserPIN, config.UserPIN)
				}
			}
		})
	}
}

func TestConfigString(t *testing.T) {
	config := &Config{
		LibraryPath: "/path/to/lib.so",
		SlotID:      5,
		UserPIN:     "secret",
	}

	str := config.String()
	expected := "PKCS11Config{LibraryPath: /path/to/lib.so, SlotID: 5, UserPIN: [REDACTED]}"
	
	if str != expected {
		t.Errorf("Expected string '%s', got '%s'", expected, str)
	}

	// Ensure PIN is redacted
	if containsSubstring(str, "secret") {
		t.Error("Config.String() should not expose the actual PIN")
	}
}

func containsSubstring(s, substr string) bool {
	return len(s) >= len(substr) && s[len(s)-len(substr):] == substr || 
		   len(s) > len(substr) && findSubstring(s, substr)
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}