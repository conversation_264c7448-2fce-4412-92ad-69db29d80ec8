package pkcs11

import (
	"context"
	"sync"
	"time"

	"github.com/miekg/pkcs11"
	"github.com/pkg/errors"
)

// Client represents a connection to a PKCS#11 device (HSM).
// It manages the PKCS#11 context, session, and authentication state.
// Client is thread-safe and can be used concurrently from multiple goroutines.
type Client struct {
	ctx       *pkcs11.Ctx
	config    *Config
	session   pkcs11.SessionHandle
	sessionMu sync.RWMutex
	loggedIn  bool
	lastUsed  time.Time
	closeOnce sync.Once
}

// NewClient creates a new PKCS#11 client with the provided configuration.
// It initializes the PKCS#11 library, opens a session, and authenticates with the device.
// The client must be closed using Close() when no longer needed.
func NewClient(config *Config) (*Client, error) {
	if err := config.Validate(); err != nil {
		return nil, errors.Wrap(err, "invalid PKCS#11 configuration")
	}

	ctx := pkcs11.New(config.LibraryPath)
	if ctx == nil {
		return nil, errors.New("failed to create PKCS#11 context")
	}

	if err := ctx.Initialize(); err != nil {
		return nil, errors.Wrap(err, "failed to initialize PKCS#11")
	}

	client := &Client{
		ctx:    ctx,
		config: config,
	}

	if err := client.connect(); err != nil {
		ctx.Finalize()
		ctx.Destroy()
		return nil, errors.Wrap(err, "failed to connect to PKCS#11 device")
	}

	return client, nil
}

// connect establishes a connection to the PKCS#11 device by finding the configured slot,
// opening a session, and logging in with the user PIN. This is called internally by NewClient.
func (c *Client) connect() error {
	c.sessionMu.Lock()
	defer c.sessionMu.Unlock()

	slots, err := c.ctx.GetSlotList(true)
	if err != nil {
		return errors.Wrap(err, "failed to get slot list")
	}

	var targetSlot uint
	found := false
	for _, slot := range slots {
		if slot == c.config.SlotID {
			targetSlot = slot
			found = true
			break
		}
	}

	if !found {
		return errors.Errorf("slot %d not found or not available", c.config.SlotID)
	}

	session, err := c.ctx.OpenSession(targetSlot, pkcs11.CKF_SERIAL_SESSION|pkcs11.CKF_RW_SESSION)
	if err != nil {
		return errors.Wrap(err, "failed to open session")
	}

	if err := c.ctx.Login(session, pkcs11.CKU_USER, c.config.UserPIN); err != nil {
		c.ctx.CloseSession(session)
		return errors.Wrap(err, "failed to login as CKU_USER")
	}

	c.session = session
	c.loggedIn = true
	c.lastUsed = time.Now()

	return nil
}

// GetSession returns the current PKCS#11 session handle.
// It validates that the client is logged in and updates the last used timestamp.
// This method is thread-safe.
func (c *Client) GetSession() (pkcs11.SessionHandle, error) {
	c.sessionMu.RLock()
	defer c.sessionMu.RUnlock()

	if !c.loggedIn {
		return 0, errors.New("not logged in to PKCS#11 device")
	}

	c.lastUsed = time.Now()
	return c.session, nil
}

// GetContext returns the underlying PKCS#11 context.
// This can be used for advanced operations not covered by the high-level API.
func (c *Client) GetContext() *pkcs11.Ctx {
	return c.ctx
}

// IsConnected returns true if the client is currently logged in to the PKCS#11 device.
// This method is thread-safe.
func (c *Client) IsConnected() bool {
	c.sessionMu.RLock()
	defer c.sessionMu.RUnlock()
	return c.loggedIn
}

// Ping tests the connection to the PKCS#11 device by performing a simple session info query.
// It returns an error if the device is not accessible or the session is invalid.
func (c *Client) Ping(ctx context.Context) error {
	session, err := c.GetSession()
	if err != nil {
		return err
	}

	_, err = c.ctx.GetSessionInfo(session)
	if err != nil {
		return errors.Wrap(err, "PKCS#11 ping failed")
	}

	return nil
}

// Close properly shuts down the PKCS#11 client by logging out, closing the session,
// finalizing the context, and destroying the PKCS#11 context.
// This method is safe to call multiple times and is thread-safe.
func (c *Client) Close() error {
	var finalErr error

	c.closeOnce.Do(func() {
		c.sessionMu.Lock()
		defer c.sessionMu.Unlock()

		if c.loggedIn && c.session != 0 {
			if err := c.ctx.Logout(c.session); err != nil {
				finalErr = errors.Wrap(err, "failed to logout")
			}

			if err := c.ctx.CloseSession(c.session); err != nil {
				if finalErr == nil {
					finalErr = errors.Wrap(err, "failed to close session")
				}
			}

			c.loggedIn = false
			c.session = 0
		}

		if err := c.ctx.Finalize(); err != nil {
			if finalErr == nil {
				finalErr = errors.Wrap(err, "failed to finalize PKCS#11")
			}
		}

		c.ctx.Destroy()
	})

	return finalErr
}
