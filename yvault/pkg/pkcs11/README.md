# PKCS#11 Go Package

A comprehensive Go wrapper for PKCS#11 (Cryptoki) operations with Hardware Security Module (HSM) support.

## Overview

This package provides a high-level, thread-safe interface for interacting with PKCS#11 compliant devices such as Hardware Security Modules (HSMs), smart cards, and other cryptographic tokens.

## Features

- **Complete Key Management**: Generate, import, find, and list RSA/ECDSA key pairs and AES/DES/3DES symmetric keys
- **Digital Signing**: Full support for RSA (PKCS#1 v1.5, PSS) and ECDSA signatures with multiple hash algorithms
- **Encryption/Decryption**: RSA encryption with PKCS#1 v1.5 and OAEP padding schemes
- **Symmetric Cryptography**: AES/DES encryption, decryption, key wrapping, and unwrapping
- **Security-First Design**: Keys marked as non-extractable and sensitive by default
- **Comprehensive Error Handling**: Categorized error codes and helper functions
- **Thread Safety**: Safe for concurrent use across multiple goroutines
- **Easy Configuration**: Support for environment variables and programmatic configuration

## Supported Algorithms

### Asymmetric Keys
- **RSA**: 2048, 4096 bits
- **ECDSA**: P-256, P-384 curves

### Hash Functions
- SHA-1, SHA-224, SHA-256, SHA-384, SHA-512

### Symmetric Keys  
- **AES**: 128, 192, 256 bits
- **DES**: 64 bits
- **3DES**: 192 bits

### Padding Schemes
- **RSA PKCS#1 v1.5** (signing and encryption)
- **RSA PSS** (signing)
- **RSA OAEP** (encryption)

## Quick Start

### Configuration

```go
// From environment variables
config, err := pkcs11.NewConfigFromEnv()

// Or programmatically
config := pkcs11.NewConfig("/usr/lib/pkcs11/libpkcs11.so", 0, "userPIN")
```

### Environment Variables

```bash
export PKCS11_LIBRARY_PATH="/usr/lib/pkcs11/libpkcs11.so"
export PKCS11_SLOT_ID="0" 
export PKCS11_USER_PIN="your-pin"
```

### Basic Usage

```go
// Create client
client, err := pkcs11.NewClient(config)
if err != nil {
    log.Fatal(err)
}
defer client.Close()

// Generate RSA key pair
keyPair, err := client.GenerateRSAKeyPair("my-key", 2048)
if err != nil {
    log.Fatal(err)
}

// Sign data with automatic hashing
signer, err := client.GetHashingSigner("my-key", crypto.SHA256)
if err != nil {
    log.Fatal(err)
}

data := []byte("Hello, World!")
signature, err := signer.Sign(rand.Reader, data, crypto.SHA256)
if err != nil {
    log.Fatal(err)
}
```

## Documentation

Complete API documentation is available via `go doc`:

```bash
go doc github.com/your-repo/pkg/pkcs11
go doc github.com/your-repo/pkg/pkcs11.Client
go doc github.com/your-repo/pkg/pkcs11.KeyPair
```

## Testing

This package includes comprehensive tests including unit tests, integration tests, and examples:

```bash
# Run unit tests
go test ./...

# Run with race detection
go test -race ./...

# Run integration tests (requires HSM/SoftHSM)
go test -tags=integration ./...
```

## Security Considerations

- All private keys are marked as **non-extractable** and **sensitive**
- Cryptographic operations are performed entirely within the HSM
- Session management includes proper cleanup and logout procedures
- Error messages avoid leaking sensitive information
- Thread-safe design prevents session corruption

## HSM Compatibility

Tested with:

- SoftHSM 2.x (development/testing)
- Utimaco HSMs (production)
- Other PKCS#11 v2.x compliant devices

## License

This package is part of the YVault project and follows the same licensing terms.

## Contributing

Please see the main YVault project for contribution guidelines.