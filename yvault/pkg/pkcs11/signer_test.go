package pkcs11

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"math/big"
	"testing"
)

func TestNewPKCS11Signer(t *testing.T) {
	client := &Client{} // Mock client
	keyPair := &KeyPair{
		Handle:    1000,
		Label:     "test-key",
		ID:        []byte("test-id"),
		KeyType:   KeyPairTypeRSA,
		KeySize:   2048,
		PublicKey: nil,
	}

	signer := NewPKCS11Signer(client, keyPair)

	if signer == nil {
		t.Fatal("Expected signer to be created")
	}
	if signer.client != client {
		t.Error("Expected signer to have the correct client")
	}
	if signer.keyPair != keyPair {
		t.Error("Expected signer to have the correct keyPair")
	}
}

func TestPKCS11Signer_Public(t *testing.T) {
	// Create a test RSA key
	rsaKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	keyPair := &KeyPair{
		Handle:    1000,
		Label:     "test-key",
		ID:        []byte("test-id"),
		KeyType:   KeyPairTypeRSA,
		KeySize:   2048,
		PublicKey: &rsaKey.PublicKey,
	}

	signer := NewPKCS11Signer(&Client{}, keyPair)
	publicKey := signer.Public()

	if publicKey != keyPair.PublicKey {
		t.Error("Expected Public() to return the keyPair's public key")
	}

	// Verify it's the correct RSA public key
	rsaPub, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		t.Fatal("Expected RSA public key")
	}
	if rsaPub.N.Cmp(rsaKey.PublicKey.N) != 0 {
		t.Error("Public key modulus doesn't match")
	}
	if rsaPub.E != rsaKey.PublicKey.E {
		t.Error("Public key exponent doesn't match")
	}
}

func TestPKCS11Signer_getRSASignMechanism(t *testing.T) {
	signer := &PKCS11Signer{
		keyPair: &KeyPair{KeyType: KeyPairTypeRSA},
	}

	tests := []struct {
		name            string
		opts            crypto.SignerOpts
		expectedMech    uint
		expectedDigestLen int
		wantErr         bool
	}{
		{
			name:            "SHA-1",
			opts:            crypto.SHA1,
			expectedMech:    0x00000006, // CKM_SHA1_RSA_PKCS
			expectedDigestLen: 20,
			wantErr:         false,
		},
		{
			name:            "SHA-224",
			opts:            crypto.SHA224,
			expectedMech:    0x00000046, // CKM_SHA224_RSA_PKCS
			expectedDigestLen: 28,
			wantErr:         false,
		},
		{
			name:            "SHA-256",
			opts:            crypto.SHA256,
			expectedMech:    0x00000040, // CKM_SHA256_RSA_PKCS
			expectedDigestLen: 32,
			wantErr:         false,
		},
		{
			name:            "SHA-384",
			opts:            crypto.SHA384,
			expectedMech:    0x00000041, // CKM_SHA384_RSA_PKCS
			expectedDigestLen: 48,
			wantErr:         false,
		},
		{
			name:            "SHA-512",
			opts:            crypto.SHA512,
			expectedMech:    0x00000042, // CKM_SHA512_RSA_PKCS
			expectedDigestLen: 64,
			wantErr:         false,
		},
		{
			name: "RSA-PSS SHA-256 (uses regular PKCS due to implementation)",
			opts: &rsa.PSSOptions{
				SaltLength: rsa.PSSSaltLengthAuto,
				Hash:       crypto.SHA256,
			},
			expectedMech:    0x00000040, // CKM_SHA256_RSA_PKCS (actual behavior)
			expectedDigestLen: 32,
			wantErr:         false,
		},
		{
			name: "RSA-PSS SHA-384 (uses regular PKCS due to implementation)",
			opts: &rsa.PSSOptions{
				SaltLength: rsa.PSSSaltLengthAuto,
				Hash:       crypto.SHA384,
			},
			expectedMech:    0x00000041, // CKM_SHA384_RSA_PKCS (actual behavior)
			expectedDigestLen: 48,
			wantErr:         false,
		},
		{
			name: "RSA-PSS SHA-512 (uses regular PKCS due to implementation)",
			opts: &rsa.PSSOptions{
				SaltLength: rsa.PSSSaltLengthAuto,
				Hash:       crypto.SHA512,
			},
			expectedMech:    0x00000042, // CKM_SHA512_RSA_PKCS (actual behavior)
			expectedDigestLen: 64,
			wantErr:         false,
		},
		{
			name: "RSA-PSS with SHA-1 (uses regular PKCS)",
			opts: &rsa.PSSOptions{
				SaltLength: rsa.PSSSaltLengthAuto,
				Hash:       crypto.SHA1,
			},
			expectedMech:    0x00000006, // CKM_SHA1_RSA_PKCS (actual behavior)
			expectedDigestLen: 20,
			wantErr:         false,
		},
		{
			name:    "unsupported hash",
			opts:    crypto.MD5,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mechanism, digestLen, err := signer.getRSASignMechanism(tt.opts)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("getRSASignMechanism() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil {
				if mechanism.Mechanism != tt.expectedMech {
					t.Errorf("Expected mechanism 0x%08X, got 0x%08X", tt.expectedMech, mechanism.Mechanism)
				}
				if digestLen != tt.expectedDigestLen {
					t.Errorf("Expected digest length %d, got %d", tt.expectedDigestLen, digestLen)
				}
			}
		})
	}
}

func TestPKCS11Signer_getECDSASignMechanism(t *testing.T) {
	signer := &PKCS11Signer{
		keyPair: &KeyPair{KeyType: KeyPairTypeECDSA},
	}

	tests := []struct {
		name            string
		opts            crypto.SignerOpts
		expectedDigestLen int
		wantErr         bool
	}{
		{
			name:            "SHA-1",
			opts:            crypto.SHA1,
			expectedDigestLen: 20,
			wantErr:         false,
		},
		{
			name:            "SHA-224",
			opts:            crypto.SHA224,
			expectedDigestLen: 28,
			wantErr:         false,
		},
		{
			name:            "SHA-256",
			opts:            crypto.SHA256,
			expectedDigestLen: 32,
			wantErr:         false,
		},
		{
			name:            "SHA-384",
			opts:            crypto.SHA384,
			expectedDigestLen: 48,
			wantErr:         false,
		},
		{
			name:            "SHA-512",
			opts:            crypto.SHA512,
			expectedDigestLen: 64,
			wantErr:         false,
		},
		{
			name:    "unsupported hash",
			opts:    crypto.MD5,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mechanism, digestLen, err := signer.getECDSASignMechanism(tt.opts)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("getECDSASignMechanism() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil {
				// ECDSA always uses CKM_ECDSA mechanism
				expectedMech := uint(0x00001041) // CKM_ECDSA
				if mechanism.Mechanism != expectedMech {
					t.Errorf("Expected mechanism 0x%08X, got 0x%08X", expectedMech, mechanism.Mechanism)
				}
				if digestLen != tt.expectedDigestLen {
					t.Errorf("Expected digest length %d, got %d", tt.expectedDigestLen, digestLen)
				}
			}
		})
	}
}

func TestPKCS11Signer_convertECDSASignatureToDER(t *testing.T) {
	signer := &PKCS11Signer{}

	tests := []struct {
		name      string
		signature []byte
		wantErr   bool
	}{
		{
			name: "valid 64-byte signature",
			signature: []byte{
				// r (32 bytes)
				0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
				0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
				0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
				0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
				// s (32 bytes)
				0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10,
				0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10,
				0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10,
				0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10,
			},
			wantErr: false,
		},
		{
			name:      "odd length signature",
			signature: []byte{0x01, 0x02, 0x03},
			wantErr:   true,
		},
		{
			name:      "empty signature",
			signature: []byte{},
			wantErr:   true,
		},
		{
			name: "signature with leading zero in r",
			signature: []byte{
				// r (32 bytes, starts with 0x80 - needs padding)
				0x80, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
				0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
				0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
				0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
				// s (32 bytes)
				0x7e, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10,
				0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10,
				0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10,
				0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := signer.convertECDSASignatureToDER(tt.signature)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("convertECDSASignatureToDER() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil {
				// Basic DER format validation
				if len(result) < 6 {
					t.Error("DER signature too short")
				}
				if result[0] != 0x30 {
					t.Error("DER signature should start with 0x30")
				}

				// Extract r and s components and verify they're not zero
				if len(tt.signature) == 64 {
					halfLen := len(tt.signature) / 2
					r := new(big.Int).SetBytes(tt.signature[:halfLen])
					s := new(big.Int).SetBytes(tt.signature[halfLen:])

					if r.Sign() == 0 || s.Sign() == 0 {
						t.Error("Neither r nor s should be zero")
					}
				}
			}
		})
	}
}

func TestNewHashingSigner(t *testing.T) {
	// Create a mock signer
	rsaKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	keyPair := &KeyPair{
		PublicKey: &rsaKey.PublicKey,
	}
	baseSigner := NewPKCS11Signer(&Client{}, keyPair)

	hashingSigner := NewHashingSigner(baseSigner, crypto.SHA256)

	if hashingSigner == nil {
		t.Fatal("Expected hashing signer to be created")
	}
	if hashingSigner.signer != baseSigner {
		t.Error("Expected hashing signer to wrap the base signer")
	}
	if hashingSigner.hash != crypto.SHA256 {
		t.Error("Expected hashing signer to have SHA256 hash")
	}
}

func TestHashingSigner_Public(t *testing.T) {
	rsaKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	keyPair := &KeyPair{
		PublicKey: &rsaKey.PublicKey,
	}
	baseSigner := NewPKCS11Signer(&Client{}, keyPair)
	hashingSigner := NewHashingSigner(baseSigner, crypto.SHA256)

	publicKey := hashingSigner.Public()
	if publicKey != keyPair.PublicKey {
		t.Error("Expected hashing signer to return the same public key")
	}
}

func TestHashingSigner_Hash(t *testing.T) {
	baseSigner := NewPKCS11Signer(&Client{}, &KeyPair{})
	hashingSigner := NewHashingSigner(baseSigner, crypto.SHA384)

	if hashingSigner.Hash() != crypto.SHA384 {
		t.Error("Expected Hash() to return the configured hash function")
	}
}

func TestNewSignerOpts(t *testing.T) {
	opts := NewSignerOpts(crypto.SHA256)
	if opts.HashFunc() != crypto.SHA256 {
		t.Error("Expected signer opts to return SHA256")
	}
}

// Test the interface compliance
func TestPKCS11Signer_ImplementsCryptoSigner(t *testing.T) {
	var _ crypto.Signer = &PKCS11Signer{}
}

func TestHashingSigner_ImplementsCryptoSigner(t *testing.T) {
	var _ crypto.Signer = &HashingSigner{}
}

// Test signature validation logic (without actual PKCS#11 calls)
func TestSignatureValidation_Logic(t *testing.T) {
	tests := []struct {
		name       string
		digest     []byte
		expected   int
		hashFunc   crypto.Hash
		shouldFail bool
	}{
		{
			name:       "SHA256 correct length",
			digest:     make([]byte, 32),
			expected:   32,
			hashFunc:   crypto.SHA256,
			shouldFail: false,
		},
		{
			name:       "SHA256 wrong length",
			digest:     make([]byte, 20),
			expected:   32,
			hashFunc:   crypto.SHA256,
			shouldFail: true,
		},
		{
			name:       "SHA384 correct length",
			digest:     make([]byte, 48),
			expected:   48,
			hashFunc:   crypto.SHA384,
			shouldFail: false,
		},
		{
			name:       "SHA512 correct length",
			digest:     make([]byte, 64),
			expected:   64,
			hashFunc:   crypto.SHA512,
			shouldFail: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// This simulates the validation logic in the Sign method
			if len(tt.digest) != tt.expected {
				if !tt.shouldFail {
					t.Errorf("Expected digest length validation to pass, but would fail")
				}
			} else {
				if tt.shouldFail {
					t.Errorf("Expected digest length validation to fail, but would pass")
				}
			}
		})
	}
}

// Mock client for basic testing
type MockClient struct {
	sessionHandle uint
	connected     bool
}

func (m *MockClient) GetSession() (uint, error) {
	if !m.connected {
		return 0, NewPKCS11Error(ErrNotInitialized, "not connected", nil)
	}
	return m.sessionHandle, nil
}

func TestBasicSigningFlow_WithMockClient(t *testing.T) {
	// This tests the basic flow without actual PKCS#11 operations
	mockClient := &MockClient{
		sessionHandle: 123,
		connected:     true,
	}

	// Test that we can get a session
	session, err := mockClient.GetSession()
	if err != nil {
		t.Errorf("Expected to get session, got error: %v", err)
	}
	if session != 123 {
		t.Errorf("Expected session 123, got %d", session)
	}

	// Test error case
	mockClient.connected = false
	_, err = mockClient.GetSession()
	if err == nil {
		t.Error("Expected error when not connected")
	}

	if !IsPKCS11Error(err, ErrNotInitialized) {
		t.Error("Expected ErrNotInitialized error")
	}
}