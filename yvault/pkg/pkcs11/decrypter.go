package pkcs11

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"io"

	"github.com/miekg/pkcs11"
	"github.com/pkg/errors"
)

// PKCS11Decrypter implements the crypto.Decrypter interface for PKCS#11 devices.
// It performs RSA decryption using private keys stored in the HSM.
// Note: ECDSA keys do not support encryption/decryption operations.
type PKCS11Decrypter struct {
	client  *Client
	keyPair *KeyPair
}

// NewPKCS11Decrypter creates a new PKCS11Decrypter for the given RSA key pair.
// Returns an error if the key pair is not an RSA key.
func NewPKCS11Decrypter(client *Client, keyPair *KeyPair) (*PKCS11Decrypter, error) {
	if keyPair.KeyType != KeyPairTypeRSA {
		return nil, errors.New("decryption is only supported for RSA keys")
	}

	return &PKCS11Decrypter{
		client:  client,
		keyPair: keyPair,
	}, nil
}

// Public returns the public key corresponding to the private key used for decryption.
func (h *PKCS11Decrypter) Public() crypto.PublicKey {
	return h.keyPair.PublicKey
}

// Decrypt decrypts the provided ciphertext using the RSA private key stored in the HSM.
// Supports PKCS#1 v1.5 and OAEP padding schemes based on the opts parameter.
// If opts is nil, PKCS#1 v1.5 padding is used by default.
func (h *PKCS11Decrypter) Decrypt(rand io.Reader, ciphertext []byte, opts crypto.DecrypterOpts) ([]byte, error) {
	session, err := h.client.GetSession()
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	var mechanism *pkcs11.Mechanism

	if opts == nil {
		mechanism = pkcs11.NewMechanism(pkcs11.CKM_RSA_PKCS, nil)
	} else {
		switch opt := opts.(type) {
		case *rsa.PKCS1v15DecryptOptions:
			mechanism = pkcs11.NewMechanism(pkcs11.CKM_RSA_PKCS, nil)
		case *rsa.OAEPOptions:
			var mechanismType uint
			switch opt.Hash {
			case crypto.SHA1:
				mechanismType = pkcs11.CKM_RSA_PKCS_OAEP
			case crypto.SHA256:
				mechanismType = pkcs11.CKM_RSA_PKCS_OAEP
			default:
				return nil, errors.New("unsupported hash function for RSA-OAEP")
			}

			var oaepParams *pkcs11.OAEPParams
			switch opt.Hash {
			case crypto.SHA1:
				oaepParams = &pkcs11.OAEPParams{
					HashAlg:    pkcs11.CKM_SHA_1,
					MGF:        pkcs11.CKG_MGF1_SHA1,
					SourceType: pkcs11.CKZ_DATA_SPECIFIED,
					SourceData: nil,
				}
			case crypto.SHA256:
				oaepParams = &pkcs11.OAEPParams{
					HashAlg:    pkcs11.CKM_SHA256,
					MGF:        pkcs11.CKG_MGF1_SHA256,
					SourceType: pkcs11.CKZ_DATA_SPECIFIED,
					SourceData: nil,
				}
			}

			if len(opt.Label) > 0 {
				oaepParams.SourceData = opt.Label
			}

			mechanism = pkcs11.NewMechanism(mechanismType, oaepParams)
		default:
			return nil, errors.New("unsupported decryption options")
		}
	}

	if err := h.client.ctx.DecryptInit(session, []*pkcs11.Mechanism{mechanism}, h.keyPair.Handle); err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	plaintext, err := h.client.ctx.Decrypt(session, ciphertext)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	return plaintext, nil
}

// RSADecrypter provides convenient methods for RSA decryption with specific padding schemes.
// It wraps PKCS11Decrypter and adds helper methods for common RSA decryption operations.
type RSADecrypter struct {
	*PKCS11Decrypter
}

// NewRSADecrypter creates a new RSADecrypter for the given RSA key pair.
func NewRSADecrypter(client *Client, keyPair *KeyPair) (*RSADecrypter, error) {
	decrypter, err := NewPKCS11Decrypter(client, keyPair)
	if err != nil {
		return nil, err
	}

	return &RSADecrypter{PKCS11Decrypter: decrypter}, nil
}

// DecryptPKCS1v15 decrypts ciphertext using RSA with PKCS#1 v1.5 padding.
func (r *RSADecrypter) DecryptPKCS1v15(ciphertext []byte) ([]byte, error) {
	opts := &rsa.PKCS1v15DecryptOptions{}
	return r.Decrypt(rand.Reader, ciphertext, opts)
}

// DecryptOAEP decrypts ciphertext using RSA with OAEP padding.
// The hash parameter specifies the hash function used (SHA-1 or SHA-256).
// The label parameter can be nil for no label, or specify additional authenticated data.
func (r *RSADecrypter) DecryptOAEP(hash crypto.Hash, ciphertext []byte, label []byte) ([]byte, error) {
	opts := &rsa.OAEPOptions{
		Hash:  hash,
		Label: label,
	}
	return r.Decrypt(rand.Reader, ciphertext, opts)
}

// GetDecrypter returns a crypto.Decrypter for the RSA key pair with the specified label.
func (c *Client) GetDecrypter(keyLabel string) (crypto.Decrypter, error) {
	keyPair, err := c.FindKeyPairByLabel(keyLabel)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to find key with label: %s", keyLabel)
	}

	return NewPKCS11Decrypter(c, keyPair)
}

// GetDecrypterByID returns a crypto.Decrypter for the RSA key pair with the specified ID.
func (c *Client) GetDecrypterByID(keyID []byte) (crypto.Decrypter, error) {
	keyPair, err := c.FindKeyPairByID(keyID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to find key by ID")
	}

	return NewPKCS11Decrypter(c, keyPair)
}

// GetRSADecrypter returns an RSADecrypter with convenient helper methods for the RSA key pair with the specified label.
func (c *Client) GetRSADecrypter(keyLabel string) (*RSADecrypter, error) {
	keyPair, err := c.FindKeyPairByLabel(keyLabel)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to find key with label: %s", keyLabel)
	}

	return NewRSADecrypter(c, keyPair)
}
