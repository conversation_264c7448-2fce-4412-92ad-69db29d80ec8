package e2e

import (
	"bytes"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/miekg/pkcs11"
	yvaultpkcs11 "github.com/qcu266/labs/yvault/pkg/pkcs11"
)

// TestE2E_SoftHSM_SecurityFeatures tests security-specific functionality
func TestE2E_SoftHSM_SecurityFeatures(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing security features with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test key non-extractability
	t.Run("KeyNonExtractability", func(t *testing.T) {
		// Generate keys and verify they are marked as non-extractable
		rsaKey, err := client.GenerateRSAKeyPair("security-test-rsa", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		aesKey, err := client.GenerateAESKey("security-test-aes", 256)
		if err != nil {
			t.Fatalf("Failed to generate AES key: %v", err)
		}

		// Verify that keys are properly protected
		// (In a real HSM, attempts to extract private key material would fail)
		t.Logf("Generated non-extractable RSA key: %s", rsaKey.Label)
		t.Logf("Generated non-extractable AES key: %s", aesKey.Label)

		// Verify keys can be used for their intended operations
		signer := yvaultpkcs11.NewPKCS11Signer(client, rsaKey)
		testData := []byte("non-extractability test")
		hash := sha256.Sum256(testData)

		_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			t.Fatalf("Non-extractable key should still be usable for signing: %v", err)
		}

		// Test AES encryption with non-extractable key
		iv := make([]byte, 16)
		_, err = rand.Read(iv)
		if err != nil {
			t.Fatalf("Failed to generate IV: %v", err)
		}

		_, err = client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
		if err != nil {
			t.Fatalf("Non-extractable AES key should be usable for encryption: %v", err)
		}

		t.Log("Key non-extractability test completed")
	})

	// Test sensitive key handling
	t.Run("SensitiveKeyHandling", func(t *testing.T) {
		// Generate keys with sensitive attribute
		keyPair, err := client.GenerateRSAKeyPair("sensitive-test-key", 2048)
		if err != nil {
			t.Fatalf("Failed to generate sensitive RSA key: %v", err)
		}

		// Verify that sensitive keys can only be used, not read
		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

		// Test multiple signing operations to ensure key remains accessible
		for i := 0; i < 10; i++ {
			testData := []byte(fmt.Sprintf("sensitive key test %d", i))
			hash := sha256.Sum256(testData)

			_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
			if err != nil {
				t.Fatalf("Sensitive key signing operation %d failed: %v", i, err)
			}
		}

		t.Log("Sensitive key handling test completed")
	})

	// Test key isolation between sessions
	t.Run("KeyIsolationBetweenSessions", func(t *testing.T) {
		// Generate a key in the current session
		keyPair1, err := client.GenerateRSAKeyPair("isolation-test-key-1", 2048)
		if err != nil {
			t.Fatalf("Failed to generate first key: %v", err)
		}

		// Create a second client (new session)
		client2, err := yvaultpkcs11.NewClient(config)
		if err != nil {
			t.Fatalf("Failed to create second client: %v", err)
		}
		defer client2.Close()

		// Generate a key in the second session
		keyPair2, err := client2.GenerateRSAKeyPair("isolation-test-key-2", 2048)
		if err != nil {
			t.Fatalf("Failed to generate second key: %v", err)
		}

		// Both clients should be able to access their own keys
		signer1 := yvaultpkcs11.NewPKCS11Signer(client, keyPair1)
		signer2 := yvaultpkcs11.NewPKCS11Signer(client2, keyPair2)

		testData := []byte("isolation test")
		hash := sha256.Sum256(testData)

		_, err = signer1.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			t.Fatalf("First client should access its key: %v", err)
		}

		_, err = signer2.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			t.Fatalf("Second client should access its key: %v", err)
		}

		// Both clients should be able to find each other's keys (if token-based)
		foundKey1, err := client2.FindKeyPairByLabel("isolation-test-key-1")
		if err != nil {
			t.Logf("Key isolation enforced: second client cannot find first client's key: %v", err)
		} else {
			t.Logf("Token-based keys: second client can find first client's key: %s", foundKey1.Label)
		}

		t.Log("Key isolation test completed")
	})

	// Test authentication requirements
	t.Run("AuthenticationRequirements", func(t *testing.T) {
		// Test with correct authentication (current client)
		keyPair, err := client.GenerateRSAKeyPair("auth-test-key", 2048)
		if err != nil {
			t.Fatalf("Failed to generate key with correct auth: %v", err)
		}

		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
		testData := []byte("authentication test")
		hash := sha256.Sum256(testData)

		_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			t.Fatalf("Authenticated operation should succeed: %v", err)
		}

		// Test with incorrect authentication
		invalidConfig := *config
		invalidConfig.UserPIN = "wrong-pin"

		_, err = yvaultpkcs11.NewClient(&invalidConfig)
		if err == nil {
			t.Log("Note: SoftHSM may not enforce PIN validation in test mode")
		} else {
			t.Logf("Correctly rejected invalid authentication: %v", err)
		}

		t.Log("Authentication requirements test completed")
	})

	t.Log("Security features test completed successfully")
}

// TestE2E_SoftHSM_DataIntegrity tests data integrity and consistency
func TestE2E_SoftHSM_DataIntegrity(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing data integrity and consistency with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test signature consistency
	t.Run("SignatureConsistency", func(t *testing.T) {
		keyPair, err := client.GenerateRSAKeyPair("integrity-test-key", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
		publicKey := signer.Public().(*rsa.PublicKey)

		// Sign the same data multiple times and verify all signatures
		testData := []byte("consistency test data")
		hash := sha256.Sum256(testData)

		const numSignatures = 10
		signatures := make([][]byte, numSignatures)

		for i := 0; i < numSignatures; i++ {
			sig, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
			if err != nil {
				t.Fatalf("Signing operation %d failed: %v", i, err)
			}
			signatures[i] = sig

			// Verify each signature immediately
			err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], sig)
			if err != nil {
				t.Fatalf("Signature %d verification failed: %v", i, err)
			}
		}

		// Verify all signatures are different (due to randomization in signing)
		for i := 0; i < numSignatures; i++ {
			for j := i + 1; j < numSignatures; j++ {
				if bytes.Equal(signatures[i], signatures[j]) {
					t.Errorf("Signatures %d and %d are identical (should be different)", i, j)
				}
			}
		}

		t.Logf("Generated and verified %d consistent signatures", numSignatures)
	})

	// Test encryption/decryption roundtrip integrity
	t.Run("EncryptionRoundtripIntegrity", func(t *testing.T) {
		aesKey, err := client.GenerateAESKey("roundtrip-test-aes", 256)
		if err != nil {
			t.Fatalf("Failed to generate AES key: %v", err)
		}

		// Test with various data sizes
		testSizes := []int{1, 16, 64, 256, 1024, 4096}

		for _, size := range testSizes {
			t.Run(fmt.Sprintf("Size%d", size), func(t *testing.T) {
				// Generate random test data
				testData := make([]byte, size)
				_, err = rand.Read(testData)
				if err != nil {
					t.Fatalf("Failed to generate test data: %v", err)
				}

				// Generate random IV
				iv := make([]byte, 16)
				_, err = rand.Read(iv)
				if err != nil {
					t.Fatalf("Failed to generate IV: %v", err)
				}

				// Encrypt
				ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
				if err != nil {
					t.Fatalf("Encryption failed for size %d: %v", size, err)
				}

				// Decrypt
				decrypted, err := client.DecryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, ciphertext)
				if err != nil {
					t.Fatalf("Decryption failed for size %d: %v", size, err)
				}

				// Verify integrity
				if !bytes.Equal(testData, decrypted) {
					t.Errorf("Data integrity failed for size %d", size)
				}

				t.Logf("Roundtrip integrity verified for %d bytes", size)
			})
		}
	})

	// Test deterministic operations
	t.Run("DeterministicOperations", func(t *testing.T) {
		aesKey, err := client.GenerateAESKey("deterministic-test-aes", 256)
		if err != nil {
			t.Fatalf("Failed to generate AES key: %v", err)
		}

		testData := []byte("deterministic test data")
		iv := make([]byte, 16) // Fixed IV for deterministic results
		// Note: In production, never reuse IVs!

		// Encrypt the same data with the same IV multiple times
		const numIterations = 5
		ciphertexts := make([][]byte, numIterations)

		for i := 0; i < numIterations; i++ {
			ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
			if err != nil {
				t.Fatalf("Encryption iteration %d failed: %v", i, err)
			}
			ciphertexts[i] = ciphertext
		}

		// All ciphertexts should be identical (same key, same IV, same data)
		for i := 1; i < numIterations; i++ {
			if !bytes.Equal(ciphertexts[0], ciphertexts[i]) {
				t.Errorf("Encryption iteration %d produced different result", i)
			}
		}

		t.Logf("Deterministic encryption verified across %d iterations", numIterations)
	})

	t.Log("Data integrity and consistency test completed successfully")
}

// TestE2E_SoftHSM_ErrorRecovery tests error recovery scenarios
func TestE2E_SoftHSM_ErrorRecovery(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing error recovery scenarios with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test recovery from invalid operations
	t.Run("InvalidOperationRecovery", func(t *testing.T) {
		aesKey, err := client.GenerateAESKey("recovery-test-aes", 256)
		if err != nil {
			t.Fatalf("Failed to generate AES key: %v", err)
		}

		// Try invalid encryption (wrong IV size)
		wrongIV := make([]byte, 8) // Should be 16 for AES
		_, err = client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, wrongIV, []byte("test"))
		if err == nil {
			t.Log("Invalid IV size was accepted (implementation-dependent)")
		} else {
			t.Logf("Correctly rejected invalid IV size: %v", err)
		}

		// Verify client can still perform valid operations after error
		correctIV := make([]byte, 16)
		_, err = rand.Read(correctIV)
		if err != nil {
			t.Fatalf("Failed to generate correct IV: %v", err)
		}

		testData := []byte("recovery test after error")
		_, err = client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, correctIV, testData)
		if err != nil {
			t.Fatalf("Valid operation should work after error: %v", err)
		}

		t.Log("Client recovered successfully from invalid operation")
	})

	// Test typed error handling
	t.Run("TypedErrorHandling", func(t *testing.T) {
		// Test key not found error
		_, err := client.FindKeyPairByLabel("non-existent-key-12345")
		if err != nil {
			if yvaultpkcs11.IsKeyNotFoundError(err) {
				t.Log("Correctly identified key not found error")
			} else {
				t.Logf("Key not found returned different error type: %v", err)
			}
		} else {
			t.Error("Expected key not found error")
		}

		// Test authentication error (if possible to trigger)
		invalidConfig := *config
		invalidConfig.UserPIN = "definitely-wrong-pin-123456"

		_, err = yvaultpkcs11.NewClient(&invalidConfig)
		if err != nil {
			if yvaultpkcs11.IsAuthenticationError(err) {
				t.Log("Correctly identified authentication error")
			} else {
				t.Logf("Authentication failure returned: %v", err)
			}
		}

		t.Log("Typed error handling test completed")
	})

	// Test operation timeout handling
	t.Run("OperationTimeouts", func(t *testing.T) {
		keyPair, err := client.GenerateRSAKeyPair("timeout-test-key", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

		// Perform many operations quickly to test timeout resistance
		const rapidOperations = 100
		start := time.Now()

		for i := 0; i < rapidOperations; i++ {
			testData := []byte(fmt.Sprintf("timeout test %d", i))
			hash := sha256.Sum256(testData)

			_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
			if err != nil {
				t.Fatalf("Rapid operation %d failed: %v", i, err)
			}
		}

		duration := time.Since(start)
		t.Logf("Completed %d rapid operations in %v (avg: %v per op)",
			rapidOperations, duration, duration/rapidOperations)
	})

	t.Log("Error recovery test completed successfully")
}

// TestE2E_SoftHSM_PerformanceRegression tests for performance regressions
func TestE2E_SoftHSM_PerformanceRegression(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing performance regression scenarios with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Performance baseline for RSA signing
	t.Run("RSASigningPerformance", func(t *testing.T) {
		keyPair, err := client.GenerateRSAKeyPair("perf-test-rsa", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
		testData := []byte("performance test data")
		hash := sha256.Sum256(testData)

		// Warmup
		for i := 0; i < 10; i++ {
			_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
			if err != nil {
				t.Fatalf("Warmup signing failed: %v", err)
			}
		}

		// Measure performance
		const perfOperations = 100
		start := time.Now()

		for i := 0; i < perfOperations; i++ {
			_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
			if err != nil {
				t.Fatalf("Performance test signing %d failed: %v", i, err)
			}
		}

		duration := time.Since(start)
		opsPerSecond := float64(perfOperations) / duration.Seconds()

		t.Logf("RSA signing performance: %.2f ops/sec (%v total for %d operations)",
			opsPerSecond, duration, perfOperations)

		// Basic performance sanity check (should be able to do at least 10 ops/sec)
		if opsPerSecond < 10 {
			t.Logf("Warning: RSA signing performance seems low: %.2f ops/sec", opsPerSecond)
		}
	})

	// Performance baseline for AES encryption
	t.Run("AESEncryptionPerformance", func(t *testing.T) {
		aesKey, err := client.GenerateAESKey("perf-test-aes", 256)
		if err != nil {
			t.Fatalf("Failed to generate AES key: %v", err)
		}

		testData := make([]byte, 1024) // 1KB blocks
		_, err = rand.Read(testData)
		if err != nil {
			t.Fatalf("Failed to generate test data: %v", err)
		}

		iv := make([]byte, 16)
		_, err = rand.Read(iv)
		if err != nil {
			t.Fatalf("Failed to generate IV: %v", err)
		}

		// Warmup
		for i := 0; i < 10; i++ {
			_, err = client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
			if err != nil {
				t.Fatalf("Warmup encryption failed: %v", err)
			}
		}

		// Measure performance
		const perfOperations = 1000
		start := time.Now()

		for i := 0; i < perfOperations; i++ {
			_, err = client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
			if err != nil {
				t.Fatalf("Performance test encryption %d failed: %v", i, err)
			}
		}

		duration := time.Since(start)
		bytesPerSecond := float64(len(testData)*perfOperations) / duration.Seconds()
		opsPerSecond := float64(perfOperations) / duration.Seconds()

		t.Logf("AES encryption performance: %.2f ops/sec, %.2f MB/sec (%v total)",
			opsPerSecond, bytesPerSecond/(1024*1024), duration)

		// Basic performance sanity check
		if opsPerSecond < 100 {
			t.Logf("Warning: AES encryption performance seems low: %.2f ops/sec", opsPerSecond)
		}
	})

	t.Log("Performance regression test completed successfully")
}

// TestE2E_SoftHSM_ComplianceAndStandards tests compliance with standards
func TestE2E_SoftHSM_ComplianceAndStandards(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing PKCS#11 compliance and standards with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test PKCS#11 standard key attributes
	t.Run("PKCS11KeyAttributes", func(t *testing.T) {
		// Generate RSA key and verify standard attributes
		keyPair, err := client.GenerateRSAKeyPair("standards-test-rsa", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		// Verify key meets PKCS#11 standards
		if keyPair.Label == "" {
			t.Error("Key should have non-empty label")
		}

		if len(keyPair.ID) == 0 {
			t.Error("Key should have non-empty ID")
		}

		if keyPair.KeyType != yvaultpkcs11.KeyPairTypeRSA {
			t.Errorf("Expected RSA key type, got %v", keyPair.KeyType)
		}

		if keyPair.KeySize != 2048 {
			t.Errorf("Expected 2048-bit key, got %d-bit", keyPair.KeySize)
		}

		// Verify public key format
		if keyPair.PublicKey == nil {
			t.Error("Public key should be available")
		}

		rsaPublicKey, ok := keyPair.PublicKey.(*rsa.PublicKey)
		if !ok {
			t.Errorf("Expected *rsa.PublicKey, got %T", keyPair.PublicKey)
		}

		if rsaPublicKey.N.BitLen() != 2048 {
			t.Errorf("Expected 2048-bit modulus, got %d-bit", rsaPublicKey.N.BitLen())
		}

		t.Log("PKCS#11 key attributes compliance verified")
	})

	// Test standard cryptographic mechanisms
	t.Run("StandardCryptographicMechanisms", func(t *testing.T) {
		// Test that standard mechanisms are supported
		keyPair, err := client.GenerateRSAKeyPair("mechanisms-test-rsa", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
		testData := []byte("standard mechanisms test")

		// Test standard hash algorithms with signing
		standardHashes := []crypto.Hash{
			crypto.SHA1,   // CKM_SHA1_RSA_PKCS
			crypto.SHA256, // CKM_SHA256_RSA_PKCS
			crypto.SHA384, // CKM_SHA384_RSA_PKCS
			crypto.SHA512, // CKM_SHA512_RSA_PKCS
		}

		for _, hashAlg := range standardHashes {
			if !hashAlg.Available() {
				continue
			}

			hasher := hashAlg.New()
			hasher.Write(testData)
			digest := hasher.Sum(nil)

			_, err = signer.Sign(rand.Reader, digest, hashAlg)
			if err != nil {
				t.Errorf("Standard mechanism %v failed: %v", hashAlg, err)
			} else {
				t.Logf("Standard mechanism %v working", hashAlg)
			}
		}

		t.Log("Standard cryptographic mechanisms compliance verified")
	})

	// Test error code compliance
	t.Run("ErrorCodeCompliance", func(t *testing.T) {
		// Test that proper PKCS#11 error codes are returned

		// Test CKR_OBJECT_HANDLE_INVALID equivalent
		_, err := client.FindKeyPairByLabel("definitely-does-not-exist-12345")
		if err != nil {
			// Should be a proper "not found" type error
			if yvaultpkcs11.IsKeyNotFoundError(err) {
				t.Log("Proper key not found error returned")
			} else {
				t.Logf("Key not found returned error: %v", err)
			}
		}

		// Test proper error messages (should not leak sensitive info)
		errorMessage := err.Error()
		sensitiveTerms := []string{"pin", "password", "secret", "private"}
		for _, term := range sensitiveTerms {
			if strings.Contains(strings.ToLower(errorMessage), term) {
				t.Errorf("Error message may leak sensitive information: %s", errorMessage)
			}
		}

		t.Log("Error code compliance verified")
	})

	t.Log("PKCS#11 compliance and standards test completed successfully")
}
