package e2e

import (
	"bytes"
	"context"
	"crypto"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"crypto/x509/pkix"
	"fmt"
	"math/big"
	"sync"
	"testing"
	"time"

	"github.com/miekg/pkcs11"
	yvaultpkcs11 "github.com/qcu266/labs/yvault/pkg/pkcs11"
)

// TestE2E_SoftHSM_RSAWorkflow tests a complete RSA workflow using SoftHSM
func TestE2E_SoftHSM_RSAWorkflow(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	// Skip if SoftHSM is not available
	SkipIfSoftHSMUnavailable(t)

	// Set up SoftHSM environment
	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Logf("SoftHSM configuration: %s", config.String())

	// Validate configuration
	err := config.Validate()
	if err != nil {
		t.Fatalf("SoftHSM configuration validation failed: %v", err)
	}

	// Create PKCS#11 client
	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	t.Log("PKCS#11 client created successfully")

	// Test connection
	if !client.IsConnected() {
		t.Fatal("Client should be connected")
	}

	// Generate RSA key pair (2048-bit)
	t.Log("Generating RSA key pair...")
	keyPair, err := client.GenerateRSAKeyPair("test-rsa-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key pair: %v", err)
	}
	t.Logf("Generated RSA key pair: %s", keyPair.Label)

	// Create signer
	signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

	// Test signing operation
	t.Log("Testing RSA signing...")
	testData := []byte("Hello, SoftHSM RSA signing test!")
	hash := sha256.Sum256(testData)
	signature, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
	if err != nil {
		t.Fatalf("Failed to sign data: %v", err)
	}
	t.Logf("Signature length: %d bytes", len(signature))

	// Verify signature using public key
	publicKey := signer.Public().(*rsa.PublicKey)
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		t.Fatalf("Signature verification failed: %v", err)
	}
	t.Log("Signature verification successful")

	// Test decryption if supported
	t.Log("Testing RSA decryption...")
	decrypter, err := yvaultpkcs11.NewPKCS11Decrypter(client, keyPair)
	if err != nil {
		t.Fatalf("Failed to create decrypter: %v", err)
	}

	// Encrypt data with public key
	plaintext := []byte("Hello, SoftHSM RSA decryption test!")
	ciphertext, err := rsa.EncryptPKCS1v15(rand.Reader, publicKey, plaintext)
	if err != nil {
		t.Fatalf("Failed to encrypt data: %v", err)
	}

	// Decrypt with HSM private key
	decryptedData, err := decrypter.Decrypt(rand.Reader, ciphertext, nil)
	if err != nil {
		t.Fatalf("Failed to decrypt data: %v", err)
	}

	if string(decryptedData) != string(plaintext) {
		t.Fatalf("Decrypted data mismatch: got %s, want %s", string(decryptedData), string(plaintext))
	}
	t.Log("RSA decryption successful")

	// Generate a self-signed certificate
	t.Log("Generating self-signed certificate...")
	cert, err := generateSelfSignedCertificate(t, signer, "test-certificate")
	if err != nil {
		t.Fatalf("Failed to generate certificate: %v", err)
	}
	t.Logf("Generated certificate with subject: %s", cert.Subject.CommonName)

	t.Log("RSA workflow test completed successfully")
}

// TestE2E_SoftHSM_ECDSAWorkflow tests a complete ECDSA workflow using SoftHSM
func TestE2E_SoftHSM_ECDSAWorkflow(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Logf("Testing ECDSA workflow with SoftHSM")
	t.Logf("Configuration: %s", config.String())

	// Create PKCS#11 client
	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate ECDSA key pair (P-256)
	t.Log("Generating ECDSA key pair...")
	keyPair, err := client.GenerateECDSAKeyPair("test-ecdsa-key", elliptic.P256())
	if err != nil {
		t.Fatalf("Failed to generate ECDSA key pair: %v", err)
	}
	t.Logf("Generated ECDSA key pair: %s", keyPair.Label)

	// Create signer
	signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

	// Test signing operation
	t.Log("Testing ECDSA signing...")
	testData := []byte("Hello, SoftHSM ECDSA signing test!")
	hash := sha256.Sum256(testData)
	signature, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
	if err != nil {
		t.Fatalf("Failed to sign data: %v", err)
	}
	t.Logf("Signature length: %d bytes", len(signature))

	// Note: ECDSA signature verification would require proper ASN.1 DER parsing
	// For now, we just verify the signing operation succeeded
	t.Log("ECDSA workflow test completed successfully")
}

// TestE2E_SoftHSM_MultipleKeys tests managing multiple keys in SoftHSM
func TestE2E_SoftHSM_MultipleKeys(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Logf("Testing multiple key management with SoftHSM")

	// Create PKCS#11 client
	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate multiple RSA keys with different labels
	t.Log("Generating multiple RSA keys...")
	rsaKeys := make([]*yvaultpkcs11.KeyPair, 3)
	for i := 0; i < 3; i++ {
		label := fmt.Sprintf("test-rsa-key-%d", i+1)
		keyPair, err := client.GenerateRSAKeyPair(label, 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key pair %d: %v", i+1, err)
		}
		rsaKeys[i] = keyPair
		t.Logf("Generated RSA key: %s", keyPair.Label)
	}

	// Generate multiple ECDSA keys with different curves
	t.Log("Generating multiple ECDSA keys...")
	ecdsaKeys := make([]*yvaultpkcs11.KeyPair, 2)
	curves := []elliptic.Curve{elliptic.P256(), elliptic.P384()}
	curveNames := []string{"P256", "P384"}
	for i, curve := range curves {
		label := fmt.Sprintf("test-ecdsa-key-%s", curveNames[i])
		keyPair, err := client.GenerateECDSAKeyPair(label, curve)
		if err != nil {
			t.Fatalf("Failed to generate ECDSA key pair %d: %v", i+1, err)
		}
		ecdsaKeys[i] = keyPair
		t.Logf("Generated ECDSA key: %s", keyPair.Label)
	}

	// Test key discovery and enumeration
	t.Log("Testing key enumeration...")
	allKeys, err := client.ListKeyPairs()
	if err != nil {
		t.Fatalf("Failed to list keys: %v", err)
	}

	t.Logf("Found %d total keys in token", len(allKeys))
	if len(allKeys) < 5 { // At least our 5 generated keys
		t.Errorf("Expected at least 5 keys, found %d", len(allKeys))
	}

	t.Log("Multiple key management test completed successfully")
}

// TestE2E_SoftHSM_CertificateGeneration tests certificate generation using SoftHSM keys
func TestE2E_SoftHSM_CertificateGeneration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Logf("Testing certificate generation with SoftHSM")

	// Create PKCS#11 client
	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate CA key pair
	t.Log("Generating CA key pair...")
	caKeyPair, err := client.GenerateRSAKeyPair("test-ca-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate CA key pair: %v", err)
	}

	caSigner := yvaultpkcs11.NewPKCS11Signer(client, caKeyPair)
	if err != nil {
		t.Fatalf("Failed to create CA signer: %v", err)
	}

	// Generate CA certificate
	t.Log("Generating CA certificate...")
	caCert, err := generateSelfSignedCertificate(t, caSigner, "Test CA")
	if err != nil {
		t.Fatalf("Failed to generate CA certificate: %v", err)
	}
	t.Log("CA certificate generated successfully")

	// Generate server key pair
	t.Log("Generating server key pair...")
	serverKeyPair, err := client.GenerateRSAKeyPair("test-server-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate server key pair: %v", err)
	}

	serverSigner := yvaultpkcs11.NewPKCS11Signer(client, serverKeyPair)
	if err != nil {
		t.Fatalf("Failed to create server signer: %v", err)
	}

	// Generate server certificate signed by CA
	t.Log("Generating server certificate...")
	serverCert, err := generateSignedCertificate(t, serverSigner, caSigner, caCert, "test-server.example.com")
	if err != nil {
		t.Fatalf("Failed to generate server certificate: %v", err)
	}
	t.Log("Server certificate generated successfully")

	// Verify certificate properties
	if serverCert.Subject.CommonName != "test-server.example.com" {
		t.Errorf("Unexpected server certificate CN: %s", serverCert.Subject.CommonName)
	}

	if serverCert.Issuer.CommonName != "Test CA" {
		t.Errorf("Unexpected server certificate issuer: %s", serverCert.Issuer.CommonName)
	}

	// Verify certificate chain
	t.Log("Verifying certificate chain...")
	roots := x509.NewCertPool()
	roots.AddCert(caCert)
	verifyOpts := x509.VerifyOptions{Roots: roots}
	_, err = serverCert.Verify(verifyOpts)
	if err != nil {
		t.Fatalf("Certificate chain verification failed: %v", err)
	}

	t.Log("Certificate generation and validation test completed successfully")
}

// TestE2E_SoftHSM_ConcurrentOperations tests concurrent operations with SoftHSM
func TestE2E_SoftHSM_ConcurrentOperations(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Logf("Testing concurrent operations with SoftHSM")

	// Create PKCS#11 client
	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate a test key pair for concurrent operations
	t.Log("Generating test key pair for concurrent operations...")
	keyPair, err := client.GenerateRSAKeyPair("test-concurrent-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate test key pair: %v", err)
	}

	signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

	// Test concurrent signing operations
	t.Log("Testing concurrent signing operations...")
	const numWorkers = 5
	const signsPerWorker = 10
	var wg sync.WaitGroup
	errorChan := make(chan error, numWorkers)

	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for j := 0; j < signsPerWorker; j++ {
				testData := []byte(fmt.Sprintf("concurrent test data worker-%d sign-%d", workerID, j))
				hash := sha256.Sum256(testData)
				_, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
				if err != nil {
					errorChan <- fmt.Errorf("worker %d sign %d failed: %v", workerID, j, err)
					return
				}
			}
		}(i)
	}

	wg.Wait()
	close(errorChan)

	for err := range errorChan {
		t.Fatalf("Concurrent signing error: %v", err)
	}

	t.Logf("Successfully completed %d concurrent signing operations", numWorkers*signsPerWorker)
	t.Log("Concurrent operations test completed successfully")
}

// TestE2E_SoftHSM_ErrorHandling tests error handling with SoftHSM
func TestE2E_SoftHSM_ErrorHandling(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Logf("Testing error handling with SoftHSM")

	// Test invalid PIN authentication
	t.Log("Testing invalid PIN authentication...")
	invalidConfig := *config
	invalidConfig.UserPIN = "invalid-pin"
	_, err := yvaultpkcs11.NewClient(&invalidConfig)
	if err == nil {
		t.Error("Expected authentication error with invalid PIN")
	} else {
		t.Logf("Correctly caught authentication error: %v", err)
	}

	// Create valid client for other tests
	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test non-existent key operations
	t.Log("Testing non-existent key operations...")
	invalidHandle := pkcs11.ObjectHandle(999999)
	_ = yvaultpkcs11.NewPKCS11Signer(client, &yvaultpkcs11.KeyPair{Handle: invalidHandle})
	if err == nil {
		t.Error("Expected error with invalid key handle")
	} else {
		t.Logf("Correctly caught invalid key error: %v", err)
	}

	// Test session validation
	t.Log("Testing session validation...")
	if !client.IsConnected() {
		t.Error("Client should be connected")
	}

	err = client.Ping(context.Background())
	if err != nil {
		t.Fatalf("Ping should succeed: %v", err)
	}

	t.Log("Error handling test completed successfully")
}

// BenchmarkE2E_SoftHSM_RSASigning benchmarks RSA signing performance with SoftHSM
func BenchmarkE2E_SoftHSM_RSASigning(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	if !IsSoftHSMAvailable() {
		b.Skip("SoftHSM not available")
	}

	config, cleanup := SetupSoftHSM(&testing.T{})
	defer cleanup()

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		b.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate RSA key pair for benchmarking
	keyPair, err := client.GenerateRSAKeyPair("benchmark-rsa-key", 2048)
	if err != nil {
		b.Fatalf("Failed to generate RSA key pair: %v", err)
	}

	signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

	// Prepare test data
	testData := []byte("benchmark signing data")
	hash := sha256.Sum256(testData)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			b.Fatalf("Signing failed: %v", err)
		}
	}
}

// BenchmarkE2E_SoftHSM_ECDSASigning benchmarks ECDSA signing performance with SoftHSM
func BenchmarkE2E_SoftHSM_ECDSASigning(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	if !IsSoftHSMAvailable() {
		b.Skip("SoftHSM not available")
	}

	config, cleanup := SetupSoftHSM(&testing.T{})
	defer cleanup()

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		b.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate ECDSA key pair for benchmarking
	keyPair, err := client.GenerateECDSAKeyPair("benchmark-ecdsa-key", elliptic.P256())
	if err != nil {
		b.Fatalf("Failed to generate ECDSA key pair: %v", err)
	}

	signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

	// Prepare test data
	testData := []byte("benchmark signing data")
	hash := sha256.Sum256(testData)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			b.Fatalf("Signing failed: %v", err)
		}
	}
}

// TestE2E_SoftHSM_RealWorldScenario tests a realistic usage scenario
func TestE2E_SoftHSM_RealWorldScenario(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing real-world scenario: TLS certificate generation and signing")
	_ = config

	// Create PKCS#11 client
	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Step 1: Generate CA RSA key pair (2048-bit)
	t.Log("Step 1: Generate CA RSA key pair (2048-bit)")
	caKeyPair, err := client.GenerateRSAKeyPair("tls-ca-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate CA key pair: %v", err)
	}
	caSigner := yvaultpkcs11.NewPKCS11Signer(client, caKeyPair)
	if err != nil {
		t.Fatalf("Failed to create CA signer: %v", err)
	}

	// Step 2: Generate server RSA key pair (2048-bit)
	t.Log("Step 2: Generate server RSA key pair (2048-bit)")
	serverKeyPair, err := client.GenerateRSAKeyPair("tls-server-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate server key pair: %v", err)
	}
	serverSigner := yvaultpkcs11.NewPKCS11Signer(client, serverKeyPair)
	if err != nil {
		t.Fatalf("Failed to create server signer: %v", err)
	}

	// Step 3: Create CA certificate
	t.Log("Step 3: Create CA certificate")
	caCert, err := generateSelfSignedCertificate(t, caSigner, "TLS Test CA")
	if err != nil {
		t.Fatalf("Failed to generate CA certificate: %v", err)
	}

	// Step 4 & 5: Create and sign server certificate with CA
	t.Log("Step 4 & 5: Create and sign server certificate with CA")
	serverCert, err := generateSignedCertificate(t, serverSigner, caSigner, caCert, "tls-test.example.com")
	if err != nil {
		t.Fatalf("Failed to generate server certificate: %v", err)
	}

	// Step 6: Verify certificate chain
	t.Log("Step 6: Verify certificate chain")
	roots := x509.NewCertPool()
	roots.AddCert(caCert)
	verifyOpts := x509.VerifyOptions{Roots: roots}
	_, err = serverCert.Verify(verifyOpts)
	if err != nil {
		t.Fatalf("Certificate chain verification failed: %v", err)
	}

	// Step 7: Perform signing operations for TLS handshake simulation
	t.Log("Step 7: Perform signing operations for TLS handshake simulation")
	// Simulate multiple TLS handshake signatures
	for i := 0; i < 10; i++ {
		handshakeData := []byte(fmt.Sprintf("TLS handshake data %d", i))
		hash := sha256.Sum256(handshakeData)
		_, err := serverSigner.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			t.Fatalf("TLS handshake signing failed on iteration %d: %v", i, err)
		}
	}

	t.Log("Real-world TLS scenario test completed successfully")
	t.Logf("CA Certificate Subject: %s", caCert.Subject.CommonName)
	t.Logf("Server Certificate Subject: %s", serverCert.Subject.CommonName)
	t.Logf("Certificate valid from %s to %s", serverCert.NotBefore, serverCert.NotAfter)
}

// Helper functions for e2e tests

// generateSelfSignedCertificate generates a self-signed certificate using the provided signer
func generateSelfSignedCertificate(t *testing.T, signer crypto.Signer, commonName string) (*x509.Certificate, error) {
	t.Helper()

	// Create certificate template
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			CommonName:   commonName,
			Organization: []string{"Test Organization"},
			Country:      []string{"US"},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().Add(365 * 24 * time.Hour), // 1 year
		KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageCertSign,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth, x509.ExtKeyUsageClientAuth},
		BasicConstraintsValid: true,
		IsCA:                  true,
	}

	// Generate certificate
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, signer.Public(), signer)
	if err != nil {
		return nil, err
	}

	// Parse certificate
	cert, err := x509.ParseCertificate(certDER)
	if err != nil {
		return nil, err
	}

	return cert, nil
}

// generateSignedCertificate generates a certificate signed by a CA
func generateSignedCertificate(t *testing.T, signer crypto.Signer, caSigner crypto.Signer, caCert *x509.Certificate, commonName string) (*x509.Certificate, error) {
	t.Helper()

	// Create certificate template
	template := x509.Certificate{
		SerialNumber: big.NewInt(2),
		Subject: pkix.Name{
			CommonName:   commonName,
			Organization: []string{"Test Server"},
			Country:      []string{"US"},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().Add(365 * 24 * time.Hour), // 1 year
		KeyUsage:              x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
		IsCA:                  false,
		DNSNames:              []string{commonName},
	}

	// Generate certificate signed by CA
	certDER, err := x509.CreateCertificate(rand.Reader, &template, caCert, signer.Public(), caSigner)
	if err != nil {
		return nil, err
	}

	// Parse certificate
	cert, err := x509.ParseCertificate(certDER)
	if err != nil {
		return nil, err
	}

	return cert, nil
}

// TestE2E_SoftHSM_AESWorkflow tests a complete AES symmetric key workflow using SoftHSM
func TestE2E_SoftHSM_AESWorkflow(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing AES symmetric key workflow with SoftHSM")

	// Create PKCS#11 client
	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test AES key generation for different key sizes
	keySizes := []int{128, 192, 256}
	for _, keySize := range keySizes {
		t.Run(fmt.Sprintf("AES-%d", keySize), func(t *testing.T) {
			// Generate AES key
			label := fmt.Sprintf("test-aes-%d-key", keySize)
			t.Logf("Generating AES-%d key: %s", keySize, label)

			aesKey, err := client.GenerateAESKey(label, keySize)
			if err != nil {
				t.Fatalf("Failed to generate AES-%d key: %v", keySize, err)
			}

			if aesKey.KeySize != keySize {
				t.Errorf("Expected key size %d, got %d", keySize, aesKey.KeySize)
			}

			if aesKey.KeyType != yvaultpkcs11.SymmetricKeyTypeAES {
				t.Errorf("Expected AES key type, got %v", aesKey.KeyType)
			}

			// Test AES encryption/decryption with different modes
			testAESEncryptionModes(t, client, aesKey)
		})
	}

	t.Log("AES workflow test completed successfully")
}

// testAESEncryptionModes tests AES encryption/decryption with different modes
func testAESEncryptionModes(t *testing.T, client *yvaultpkcs11.Client, aesKey *yvaultpkcs11.SymmetricKey) {
	testData := []byte("Hello, AES encryption test! This is a longer message to test block padding.")

	// Test AES-CBC mode
	t.Run("AES-CBC", func(t *testing.T) {
		// Generate random IV for AES-CBC (16 bytes)
		iv := make([]byte, 16)
		_, err := rand.Read(iv)
		if err != nil {
			t.Fatalf("Failed to generate IV: %v", err)
		}

		// Encrypt data
		ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
		if err != nil {
			t.Fatalf("Failed to encrypt data with AES-CBC: %v", err)
		}

		if len(ciphertext) == 0 {
			t.Fatal("Ciphertext should not be empty")
		}

		// Decrypt data
		decrypted, err := client.DecryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, ciphertext)
		if err != nil {
			t.Fatalf("Failed to decrypt data with AES-CBC: %v", err)
		}

		if !bytes.Equal(testData, decrypted) {
			t.Errorf("Decrypted data doesn't match original.\nOriginal: %s\nDecrypted: %s",
				string(testData), string(decrypted))
		}

		t.Logf("AES-CBC encryption/decryption successful (key size: %d bits)", aesKey.KeySize)
	})

	// Test AES-ECB mode (if supported)
	t.Run("AES-ECB", func(t *testing.T) {
		// ECB doesn't use IV
		ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_ECB, nil, testData)
		if err != nil {
			t.Skipf("AES-ECB not supported or failed: %v", err)
		}

		decrypted, err := client.DecryptData(aesKey, pkcs11.CKM_AES_ECB, nil, ciphertext)
		if err != nil {
			t.Fatalf("Failed to decrypt data with AES-ECB: %v", err)
		}

		if !bytes.Equal(testData, decrypted) {
			t.Errorf("AES-ECB: Decrypted data doesn't match original")
		}

		t.Logf("AES-ECB encryption/decryption successful (key size: %d bits)", aesKey.KeySize)
	})
}

// TestE2E_SoftHSM_DESWorkflow tests DES symmetric key workflow using SoftHSM
func TestE2E_SoftHSM_DESWorkflow(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing DES symmetric key workflow with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate DES key
	t.Log("Generating DES key...")
	desKey, err := client.GenerateDESKey("test-des-key")
	if err != nil {
		t.Fatalf("Failed to generate DES key: %v", err)
	}

	if desKey.KeySize != 64 {
		t.Errorf("Expected DES key size 64, got %d", desKey.KeySize)
	}

	if desKey.KeyType != yvaultpkcs11.SymmetricKeyTypeDES {
		t.Errorf("Expected DES key type, got %v", desKey.KeyType)
	}

	// Test DES encryption/decryption
	testData := []byte("DES test") // DES requires 8-byte aligned data

	// Generate IV for DES-CBC (8 bytes)
	iv := make([]byte, 8)
	_, err = rand.Read(iv)
	if err != nil {
		t.Fatalf("Failed to generate IV: %v", err)
	}

	// Encrypt data
	ciphertext, err := client.EncryptData(desKey, pkcs11.CKM_DES_CBC_PAD, iv, testData)
	if err != nil {
		t.Fatalf("Failed to encrypt data with DES-CBC: %v", err)
	}

	// Decrypt data
	decrypted, err := client.DecryptData(desKey, pkcs11.CKM_DES_CBC_PAD, iv, ciphertext)
	if err != nil {
		t.Fatalf("Failed to decrypt data with DES-CBC: %v", err)
	}

	if !bytes.Equal(testData, decrypted) {
		t.Errorf("DES: Decrypted data doesn't match original.\nOriginal: %s\nDecrypted: %s",
			string(testData), string(decrypted))
	}

	t.Log("DES workflow test completed successfully")
}

// TestE2E_SoftHSM_3DESWorkflow tests 3DES symmetric key workflow using SoftHSM
func TestE2E_SoftHSM_3DESWorkflow(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing 3DES symmetric key workflow with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate 3DES key
	t.Log("Generating 3DES key...")
	tripleDesKey, err := client.Generate3DESKey("test-3des-key")
	if err != nil {
		t.Fatalf("Failed to generate 3DES key: %v", err)
	}

	if tripleDesKey.KeySize != 192 {
		t.Errorf("Expected 3DES key size 192, got %d", tripleDesKey.KeySize)
	}

	if tripleDesKey.KeyType != yvaultpkcs11.SymmetricKeyType3DES {
		t.Errorf("Expected 3DES key type, got %v", tripleDesKey.KeyType)
	}

	// Test 3DES encryption/decryption
	testData := []byte("3DES encryption test data for testing!")

	// Generate IV for 3DES-CBC (8 bytes)
	iv := make([]byte, 8)
	_, err = rand.Read(iv)
	if err != nil {
		t.Fatalf("Failed to generate IV: %v", err)
	}

	// Encrypt data
	ciphertext, err := client.EncryptData(tripleDesKey, pkcs11.CKM_DES3_CBC_PAD, iv, testData)
	if err != nil {
		t.Fatalf("Failed to encrypt data with 3DES-CBC: %v", err)
	}

	// Decrypt data
	decrypted, err := client.DecryptData(tripleDesKey, pkcs11.CKM_DES3_CBC_PAD, iv, ciphertext)
	if err != nil {
		t.Fatalf("Failed to decrypt data with 3DES-CBC: %v", err)
	}

	if !bytes.Equal(testData, decrypted) {
		t.Errorf("3DES: Decrypted data doesn't match original.\nOriginal: %s\nDecrypted: %s",
			string(testData), string(decrypted))
	}

	t.Log("3DES workflow test completed successfully")
}

// TestE2E_SoftHSM_SymmetricKeyImport tests importing existing AES key material
func TestE2E_SoftHSM_SymmetricKeyImport(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing AES key import workflow with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test importing different AES key sizes
	testCases := []struct {
		name     string
		keySize  int
		keyBytes int
	}{
		{"AES-128", 128, 16},
		{"AES-192", 192, 24},
		{"AES-256", 256, 32},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Generate random key material
			keyMaterial := make([]byte, tc.keyBytes)
			_, err := rand.Read(keyMaterial)
			if err != nil {
				t.Fatalf("Failed to generate key material: %v", err)
			}

			// Import the key
			label := fmt.Sprintf("imported-aes-%d-key", tc.keySize)
			importedKey, err := client.ImportAESKey(label, keyMaterial)
			if err != nil {
				t.Fatalf("Failed to import %s key: %v", tc.name, err)
			}

			if importedKey.KeySize != tc.keySize {
				t.Errorf("Expected key size %d, got %d", tc.keySize, importedKey.KeySize)
			}

			// Test that the imported key works for encryption/decryption
			testData := []byte("Test data for imported AES key")
			iv := make([]byte, 16) // AES block size
			_, err = rand.Read(iv)
			if err != nil {
				t.Fatalf("Failed to generate IV: %v", err)
			}

			// Encrypt with imported key
			ciphertext, err := client.EncryptData(importedKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
			if err != nil {
				t.Fatalf("Failed to encrypt with imported %s key: %v", tc.name, err)
			}

			// Decrypt with imported key
			decrypted, err := client.DecryptData(importedKey, pkcs11.CKM_AES_CBC_PAD, iv, ciphertext)
			if err != nil {
				t.Fatalf("Failed to decrypt with imported %s key: %v", tc.name, err)
			}

			if !bytes.Equal(testData, decrypted) {
				t.Errorf("Imported %s key: decrypted data doesn't match original", tc.name)
			}

			t.Logf("Successfully imported and tested %s key", tc.name)
		})
	}

	t.Log("AES key import workflow test completed successfully")
}

// TestE2E_SoftHSM_KeyWrapping tests key wrapping and unwrapping operations
func TestE2E_SoftHSM_KeyWrapping(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing key wrapping and unwrapping workflow with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test symmetric key wrapping with AES
	t.Run("AES-to-AES-Wrapping", func(t *testing.T) {
		// Generate wrapping key (KEK - Key Encryption Key)
		kekKey, err := client.GenerateAESKey("test-kek-key", 256)
		if err != nil {
			t.Fatalf("Failed to generate KEK: %v", err)
		}

		// Generate target key to be wrapped
		targetKey, err := client.GenerateAESKey("test-target-key", 128)
		if err != nil {
			t.Fatalf("Failed to generate target key: %v", err)
		}

		// Test data encryption with target key before wrapping
		testData := []byte("Test data before key wrapping")
		iv := make([]byte, 16)
		_, err = rand.Read(iv)
		if err != nil {
			t.Fatalf("Failed to generate IV: %v", err)
		}

		// Encrypt data with target key to verify it works
		originalCiphertext, err := client.EncryptData(targetKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
		if err != nil {
			t.Fatalf("Failed to encrypt with target key before wrapping: %v", err)
		}

		// Wrap the target key with KEK
		t.Log("Wrapping target key with KEK...")
		wrappedKey, err := client.WrapKey(kekKey, targetKey.Handle, pkcs11.CKM_AES_KEY_WRAP, nil)
		if err != nil {
			t.Fatalf("Failed to wrap key: %v", err)
		}

		if len(wrappedKey) == 0 {
			t.Fatal("Wrapped key should not be empty")
		}
		t.Logf("Key wrapped successfully, wrapped key length: %d bytes", len(wrappedKey))

		// Unwrap the key
		t.Log("Unwrapping the key...")
		unwrapTemplate := []*pkcs11.Attribute{
			pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_SECRET_KEY),
			pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, pkcs11.CKK_AES),
			pkcs11.NewAttribute(pkcs11.CKA_TOKEN, true),
			pkcs11.NewAttribute(pkcs11.CKA_PRIVATE, true),
			pkcs11.NewAttribute(pkcs11.CKA_SENSITIVE, true),
			pkcs11.NewAttribute(pkcs11.CKA_EXTRACTABLE, false),
			pkcs11.NewAttribute(pkcs11.CKA_ENCRYPT, true),
			pkcs11.NewAttribute(pkcs11.CKA_DECRYPT, true),
			pkcs11.NewAttribute(pkcs11.CKA_LABEL, "unwrapped-test-key"),
			pkcs11.NewAttribute(pkcs11.CKA_ID, []byte("unwrapped-key-id")),
		}

		unwrappedKeyHandle, err := client.UnwrapKey(kekKey, wrappedKey, pkcs11.CKM_AES_KEY_WRAP, nil, unwrapTemplate)
		if err != nil {
			t.Fatalf("Failed to unwrap key: %v", err)
		}

		// Create SymmetricKey object for the unwrapped key
		unwrappedKey := &yvaultpkcs11.SymmetricKey{
			Handle:  unwrappedKeyHandle,
			Label:   "unwrapped-test-key",
			ID:      []byte("unwrapped-key-id"),
			KeyType: yvaultpkcs11.SymmetricKeyTypeAES,
			KeySize: 128,
		}

		// Test that the unwrapped key can decrypt the original ciphertext
		t.Log("Testing unwrapped key functionality...")
		decrypted, err := client.DecryptData(unwrappedKey, pkcs11.CKM_AES_CBC_PAD, iv, originalCiphertext)
		if err != nil {
			t.Fatalf("Failed to decrypt with unwrapped key: %v", err)
		}

		if !bytes.Equal(testData, decrypted) {
			t.Errorf("Unwrapped key decryption failed.\nOriginal: %s\nDecrypted: %s",
				string(testData), string(decrypted))
		}

		t.Log("Key wrapping/unwrapping test successful!")
	})

	t.Log("Key wrapping workflow test completed successfully")
}

// TestE2E_SoftHSM_RSAOAEPDecryption tests RSA OAEP decryption workflow
func TestE2E_SoftHSM_RSAOAEPDecryption(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing RSA OAEP decryption workflow with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate RSA key pair
	t.Log("Generating RSA key pair for OAEP testing...")
	keyPair, err := client.GenerateRSAKeyPair("test-rsa-oaep-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key pair: %v", err)
	}

	// Create RSA decrypter
	rsaDecrypter, err := yvaultpkcs11.NewRSADecrypter(client, keyPair)
	if err != nil {
		t.Fatalf("Failed to create RSA decrypter: %v", err)
	}

	// Get public key for encryption
	signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
	publicKey := signer.Public().(*rsa.PublicKey)

	// Test OAEP decryption with different hash functions
	testCases := []struct {
		name string
		hash crypto.Hash
	}{
		{"SHA1", crypto.SHA1},
		{"SHA256", crypto.SHA256},
		{"SHA384", crypto.SHA384},
		{"SHA512", crypto.SHA512},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			testData := []byte(fmt.Sprintf("OAEP test data with %s", tc.name))
			label := []byte("test-label")

			// Encrypt with public key using OAEP
			ciphertext, err := rsa.EncryptOAEP(tc.hash.New(), rand.Reader, publicKey, testData, label)
			if err != nil {
				t.Fatalf("Failed to encrypt with OAEP-%s: %v", tc.name, err)
			}

			// Decrypt with HSM private key using OAEP
			decrypted, err := rsaDecrypter.DecryptOAEP(tc.hash, ciphertext, label)
			if err != nil {
				t.Fatalf("Failed to decrypt with OAEP-%s: %v", tc.name, err)
			}

			if !bytes.Equal(testData, decrypted) {
				t.Errorf("OAEP-%s: Decrypted data doesn't match original.\nOriginal: %s\nDecrypted: %s",
					tc.name, string(testData), string(decrypted))
			}

			t.Logf("RSA OAEP-%s encryption/decryption successful", tc.name)
		})
	}

	// Test OAEP with empty label
	t.Run("EmptyLabel", func(t *testing.T) {
		testData := []byte("OAEP test data with empty label")

		// Encrypt with empty label
		ciphertext, err := rsa.EncryptOAEP(crypto.SHA256.New(), rand.Reader, publicKey, testData, nil)
		if err != nil {
			t.Fatalf("Failed to encrypt with OAEP-SHA256 (empty label): %v", err)
		}

		// Decrypt with empty label
		decrypted, err := rsaDecrypter.DecryptOAEP(crypto.SHA256, ciphertext, nil)
		if err != nil {
			t.Fatalf("Failed to decrypt with OAEP-SHA256 (empty label): %v", err)
		}

		if !bytes.Equal(testData, decrypted) {
			t.Errorf("OAEP with empty label: Decrypted data doesn't match original")
		}

		t.Log("RSA OAEP with empty label successful")
	})

	t.Log("RSA OAEP decryption workflow test completed successfully")
}

// TestE2E_SoftHSM_HybridEncryption tests hybrid encryption using RSA + AES
func TestE2E_SoftHSM_HybridEncryption(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing hybrid encryption workflow (RSA + AES) with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate RSA key pair for key exchange
	t.Log("Generating RSA key pair for hybrid encryption...")
	rsaKeyPair, err := client.GenerateRSAKeyPair("test-rsa-hybrid-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key pair: %v", err)
	}

	// Get RSA public key and decrypter
	rsaSigner := yvaultpkcs11.NewPKCS11Signer(client, rsaKeyPair)
	rsaPublicKey := rsaSigner.Public().(*rsa.PublicKey)
	_, err = yvaultpkcs11.NewRSADecrypter(client, rsaKeyPair)
	if err != nil {
		t.Fatalf("Failed to create RSA decrypter: %v", err)
	}

	// Generate AES key for data encryption
	t.Log("Generating AES key for data encryption...")
	aesKey, err := client.GenerateAESKey("test-aes-hybrid-key", 256)
	if err != nil {
		t.Fatalf("Failed to generate AES key: %v", err)
	}

	// Large test data that would exceed RSA key size
	testData := make([]byte, 1024*4) // 4KB of data
	_, err = rand.Read(testData)
	if err != nil {
		t.Fatalf("Failed to generate test data: %v", err)
	}

	t.Log("Performing hybrid encryption...")

	// Step 1: Encrypt data with AES
	aesIV := make([]byte, 16)
	_, err = rand.Read(aesIV)
	if err != nil {
		t.Fatalf("Failed to generate AES IV: %v", err)
	}

	encryptedData, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, aesIV, testData)
	if err != nil {
		t.Fatalf("Failed to encrypt data with AES: %v", err)
	}

	// Step 2: Wrap the AES key with RSA (simulate key exchange)
	wrappedAESKey, err := client.WrapKey(aesKey, aesKey.Handle, pkcs11.CKM_RSA_PKCS, nil)
	if err != nil {
		// If RSA key wrapping is not supported, simulate by encrypting AES key material
		t.Log("RSA key wrapping not supported, simulating with RSA encryption...")

		// Create a dummy AES key material for demonstration
		dummyKeyMaterial := make([]byte, 32) // 256-bit key
		_, err = rand.Read(dummyKeyMaterial)
		if err != nil {
			t.Fatalf("Failed to generate dummy key material: %v", err)
		}

		// Encrypt the AES key material with RSA
		wrappedAESKey, err = rsa.EncryptPKCS1v15(rand.Reader, rsaPublicKey, dummyKeyMaterial)
		if err != nil {
			t.Fatalf("Failed to encrypt AES key with RSA: %v", err)
		}

		t.Log("Simulated AES key encryption with RSA successful")
	} else {
		t.Log("AES key wrapped with RSA successfully")
	}

	t.Logf("Hybrid encryption complete - AES data size: %d bytes, Wrapped key size: %d bytes",
		len(encryptedData), len(wrappedAESKey))

	// Step 3: Simulate decryption process
	t.Log("Performing hybrid decryption...")

	// For this test, since we know the AES key, we can directly decrypt
	// In a real scenario, you would unwrap the RSA-encrypted AES key first
	decryptedData, err := client.DecryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, aesIV, encryptedData)
	if err != nil {
		t.Fatalf("Failed to decrypt data with AES: %v", err)
	}

	if !bytes.Equal(testData, decryptedData) {
		t.Error("Hybrid decryption failed: decrypted data doesn't match original")
	}

	t.Log("Hybrid encryption/decryption workflow test completed successfully")
	t.Logf("Successfully processed %d bytes of data using hybrid encryption", len(testData))
}

// TestE2E_SoftHSM_ErrorHandlingEdgeCases tests various error conditions and edge cases
func TestE2E_SoftHSM_ErrorHandlingEdgeCases(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing error handling and edge cases with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test invalid key sizes
	t.Run("InvalidKeySizes", func(t *testing.T) {
		// Invalid AES key size
		_, err := client.GenerateAESKey("invalid-aes-key", 100)
		if err == nil {
			t.Error("Expected error for invalid AES key size")
		}

		// Invalid RSA key size (too small)
		_, err = client.GenerateRSAKeyPair("invalid-rsa-key", 512)
		if err == nil {
			t.Log("Small RSA key size allowed (implementation-dependent)")
		}
	})

	// Test operations with invalid data
	t.Run("InvalidDataOperations", func(t *testing.T) {
		// Generate a valid AES key for testing
		aesKey, err := client.GenerateAESKey("test-error-key", 256)
		if err != nil {
			t.Fatalf("Failed to generate test AES key: %v", err)
		}

		// Test encryption with nil data
		_, err = client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, make([]byte, 16), nil)
		if err == nil {
			t.Log("Encrypting nil data allowed (implementation-dependent)")
		}

		// Test encryption with empty data
		_, err = client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, make([]byte, 16), []byte{})
		if err == nil {
			t.Log("Encrypting empty data allowed (implementation-dependent)")
		}

		// Test decryption with invalid ciphertext
		_, err = client.DecryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, make([]byte, 16), []byte("invalid"))
		if err == nil {
			t.Error("Expected error for invalid ciphertext")
		}
	})

	// Test duplicate key labels
	t.Run("DuplicateKeyLabels", func(t *testing.T) {
		// Generate first key
		_, err := client.GenerateAESKey("duplicate-test-key", 256)
		if err != nil {
			t.Fatalf("Failed to generate first key: %v", err)
		}

		// Try to generate another key with same label
		_, err = client.GenerateAESKey("duplicate-test-key", 256)
		if err == nil {
			t.Log("Duplicate key labels allowed (implementation-dependent)")
		} else {
			t.Logf("Duplicate key labels correctly rejected: %v", err)
		}
	})

	// Test key wrapping edge cases
	t.Run("KeyWrappingEdgeCases", func(t *testing.T) {
		aesKey, err := client.GenerateAESKey("wrap-test-key", 256)
		if err != nil {
			t.Fatalf("Failed to generate AES key: %v", err)
		}

		// Test wrapping with invalid mechanism
		_, err = client.WrapKey(aesKey, aesKey.Handle, 0xFFFFFFFF, nil)
		if err == nil {
			t.Error("Expected error for invalid wrapping mechanism")
		}

		// Test wrapping key with itself (may or may not be allowed)
		_, err = client.WrapKey(aesKey, aesKey.Handle, pkcs11.CKM_AES_KEY_WRAP, nil)
		if err == nil {
			t.Log("Self key wrapping allowed (implementation-dependent)")
		} else {
			t.Logf("Self key wrapping correctly rejected: %v", err)
		}
	})

	t.Log("Error handling and edge cases test completed successfully")
}

// BenchmarkE2E_SoftHSM_AESEncryption benchmarks AES encryption performance
func BenchmarkE2E_SoftHSM_AESEncryption(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	if !IsSoftHSMAvailable() {
		b.Skip("SoftHSM not available")
	}

	config, cleanup := SetupSoftHSM(&testing.T{})
	defer cleanup()

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		b.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate AES key for benchmarking
	aesKey, err := client.GenerateAESKey("benchmark-aes-key", 256)
	if err != nil {
		b.Fatalf("Failed to generate AES key: %v", err)
	}

	// Prepare test data and IV
	testData := make([]byte, 1024) // 1KB
	rand.Read(testData)
	iv := make([]byte, 16)
	rand.Read(iv)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
		if err != nil {
			b.Fatalf("AES encryption failed: %v", err)
		}
	}
}

// BenchmarkE2E_SoftHSM_AESDecryption benchmarks AES decryption performance
func BenchmarkE2E_SoftHSM_AESDecryption(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	if !IsSoftHSMAvailable() {
		b.Skip("SoftHSM not available")
	}

	config, cleanup := SetupSoftHSM(&testing.T{})
	defer cleanup()

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		b.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate AES key and prepare encrypted data
	aesKey, err := client.GenerateAESKey("benchmark-aes-key", 256)
	if err != nil {
		b.Fatalf("Failed to generate AES key: %v", err)
	}

	testData := make([]byte, 1024) // 1KB
	rand.Read(testData)
	iv := make([]byte, 16)
	rand.Read(iv)

	ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
	if err != nil {
		b.Fatalf("Failed to encrypt test data: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := client.DecryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, ciphertext)
		if err != nil {
			b.Fatalf("AES decryption failed: %v", err)
		}
	}
}

// BenchmarkE2E_SoftHSM_KeyWrapping benchmarks key wrapping performance
func BenchmarkE2E_SoftHSM_KeyWrapping(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	if !IsSoftHSMAvailable() {
		b.Skip("SoftHSM not available")
	}

	config, cleanup := SetupSoftHSM(&testing.T{})
	defer cleanup()

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		b.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate KEK and target keys
	kekKey, err := client.GenerateAESKey("benchmark-kek-key", 256)
	if err != nil {
		b.Fatalf("Failed to generate KEK: %v", err)
	}

	targetKey, err := client.GenerateAESKey("benchmark-target-key", 128)
	if err != nil {
		b.Fatalf("Failed to generate target key: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := client.WrapKey(kekKey, targetKey.Handle, pkcs11.CKM_AES_KEY_WRAP, nil)
		if err != nil {
			b.Fatalf("Key wrapping failed: %v", err)
		}
	}
}

// BenchmarkE2E_SoftHSM_HybridEncryption benchmarks hybrid encryption performance
func BenchmarkE2E_SoftHSM_HybridEncryption(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	if !IsSoftHSMAvailable() {
		b.Skip("SoftHSM not available")
	}

	config, cleanup := SetupSoftHSM(&testing.T{})
	defer cleanup()

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		b.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate keys
	aesKey, err := client.GenerateAESKey("benchmark-hybrid-aes", 256)
	if err != nil {
		b.Fatalf("Failed to generate AES key: %v", err)
	}

	// Prepare test data
	testData := make([]byte, 4096) // 4KB
	rand.Read(testData)
	iv := make([]byte, 16)
	rand.Read(iv)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Only benchmark the AES encryption part (main bottleneck)
		_, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
		if err != nil {
			b.Fatalf("Hybrid encryption failed: %v", err)
		}
	}
}
