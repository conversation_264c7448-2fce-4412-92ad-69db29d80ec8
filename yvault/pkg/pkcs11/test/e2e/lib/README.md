# SoftHSM Libraries Directory

This directory contains platform-specific SoftHSM libraries for PKCS#11 e2e testing.

## Directory Structure

```
lib/
├── README.md                    # This file
├── linux-amd64/
│   └── libsofthsm2.so          # SoftHSM library for Linux x86_64
├── linux-arm64/
│   └── libsofthsm2.so          # SoftHSM library for Linux ARM64
├── darwin-amd64/
│   ├── libsofthsm2.dylib       # SoftHSM library for macOS x86_64
│   └── libsofthsm2.so          # Symlink for compatibility
├── darwin-arm64/
│   ├── libsofthsm2.dylib       # SoftHSM library for macOS ARM64 (Apple Silicon)
│   └── libsofthsm2.so          # Symlink for compatibility
└── windows-amd64/
    └── softhsm2.dll            # SoftHSM library for Windows x86_64
```

## Installation

To install SoftHSM for your platform, run the installation script:

```bash
# From the e2e_test directory
./install-softhsm.sh
```

The script will:
1. Detect your platform automatically
2. Try to install SoftHSM using your system package manager (apt, yum, brew, etc.)
3. Fall back to building from source if package manager installation fails
4. Place the library in the appropriate platform subdirectory

### Installation Options

```bash
# Force reinstallation
./install-softhsm.sh --force

# Build from source only (skip package manager)
./install-softhsm.sh --source-only

# Use system package manager only (don't build from source)
./install-softhsm.sh --system-only

# Show help
./install-softhsm.sh --help
```

## Usage in Tests

The test code automatically discovers the appropriate SoftHSM library based on the current platform using the `getBundledSoftHSMPath()` function in `softhsm_test.go`.

## Platform Support

| Platform | Architecture | Library Name | Status |
|----------|-------------|--------------|---------|
| Linux | x86_64 | libsofthsm2.so | ✅ Supported |
| Linux | ARM64 | libsofthsm2.so | ✅ Supported |
| macOS | x86_64 | libsofthsm2.dylib | ✅ Supported |
| macOS | ARM64 | libsofthsm2.dylib | ✅ Supported |
| Windows | x86_64 | softhsm2.dll | 🔄 Planned |

## Troubleshooting

### Library Not Found
If you get errors about missing SoftHSM libraries:

1. Run the installation script: `./install-softhsm.sh`
2. Check that the library exists in the correct platform directory
3. Verify the library is not corrupted by running: `file lib/your-platform/libsofthsm2.so`

### Permission Errors
If you get permission errors during installation:

1. Make sure the script is executable: `chmod +x install-softhsm.sh`
2. You may need sudo for system package manager installations
3. For building from source, ensure you have write permissions to this directory

### Build Dependencies
If building from source fails due to missing dependencies:

- **Ubuntu/Debian**: `sudo apt-get install build-essential libssl-dev curl`
- **RHEL/CentOS**: `sudo yum install gcc make openssl-devel curl`
- **macOS**: `xcode-select --install && brew install openssl`

## Manual Installation

If the automated script doesn't work for your platform, you can manually place a SoftHSM library:

1. Install SoftHSM using your system package manager
2. Find the library location (usually `/usr/lib/softhsm/libsofthsm2.so`)
3. Create the platform directory: `mkdir -p lib/your-platform/`
4. Copy the library: `cp /path/to/libsofthsm2.so lib/your-platform/`

## Environment Variables

The following environment variables can be used to override SoftHSM behavior:

- `SOFTHSM2_CONF`: Path to SoftHSM configuration file
- `PKCS11_LIBRARY_PATH`: Override library path (takes precedence over bundled libraries)
- `PKCS11_SLOT_ID`: Override slot ID (default: 0)
- `PKCS11_USER_PIN`: Override user PIN (default: 1234)

## Notes

- Libraries in this directory are used only for testing purposes
- The bundled libraries are discovered automatically by the test code
- Each platform directory should contain only the library for that specific platform
- Symlinks are used on macOS for compatibility between .dylib and .so extensions