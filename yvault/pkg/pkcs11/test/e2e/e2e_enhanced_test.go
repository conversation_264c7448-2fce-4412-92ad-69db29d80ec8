package e2e

import (
	"bytes"
	"context"
	"crypto"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"fmt"
	"os"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/miekg/pkcs11"
	yvaultpkcs11 "github.com/qcu266/labs/yvault/pkg/pkcs11"
)

// TestE2E_SoftHSM_RSAPSSSignatures tests RSA PSS signature scheme
func TestE2E_SoftHSM_RSAPSSSignatures(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing RSA PSS signature workflow with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate RSA key pair for PSS testing
	keyPair, err := client.GenerateRSAKeyPair("test-rsa-pss-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key pair: %v", err)
	}

	signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

	// Test PSS signatures with different hash algorithms
	testCases := []struct {
		name string
		hash crypto.Hash
	}{
		{"SHA256", crypto.SHA256},
		{"SHA384", crypto.SHA384},
		{"SHA512", crypto.SHA512},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			testData := []byte(fmt.Sprintf("PSS test data with %s", tc.name))
			hash := tc.hash.New()
			hash.Write(testData)
			digest := hash.Sum(nil)

			// Create PSS options
			pssOpts := &rsa.PSSOptions{
				SaltLength: rsa.PSSSaltLengthAuto,
				Hash:       tc.hash,
			}

			// Sign with PSS
			signature, err := signer.Sign(rand.Reader, digest, pssOpts)
			if err != nil {
				t.Fatalf("Failed to sign with PSS-%s: %v", tc.name, err)
			}

			// Verify signature using public key
			publicKey := signer.Public().(*rsa.PublicKey)
			err = rsa.VerifyPSS(publicKey, tc.hash, digest, signature, pssOpts)
			if err != nil {
				t.Fatalf("PSS-%s signature verification failed: %v", tc.name, err)
			}

			t.Logf("RSA PSS-%s signature test successful", tc.name)
		})
	}

	t.Log("RSA PSS signature workflow test completed successfully")
}

// TestE2E_SoftHSM_SessionManagement tests session management edge cases
func TestE2E_SoftHSM_SessionManagement(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing session management scenarios with SoftHSM")

	// Test client connection and disconnection
	t.Run("ConnectionLifecycle", func(t *testing.T) {
		client, err := yvaultpkcs11.NewClient(config)
		if err != nil {
			t.Fatalf("Failed to create client: %v", err)
		}

		// Test connection status
		if !client.IsConnected() {
			t.Error("Client should be connected after creation")
		}

		// Test ping
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		err = client.Ping(ctx)
		if err != nil {
			t.Errorf("Ping failed: %v", err)
		}

		// Test close
		client.Close()

		// Test ping after close (should fail or handle gracefully)
		err = client.Ping(ctx)
		if err == nil {
			t.Log("Ping after close still works (implementation allows)")
		} else {
			t.Logf("Ping after close correctly failed: %v", err)
		}
	})

	// Test session timeout handling
	t.Run("SessionTimeout", func(t *testing.T) {
		client, err := yvaultpkcs11.NewClient(config)
		if err != nil {
			t.Fatalf("Failed to create client: %v", err)
		}
		defer client.Close()

		// Generate a key for testing
		keyPair, err := client.GenerateRSAKeyPair("session-timeout-key", 2048)
		if err != nil {
			t.Fatalf("Failed to generate key pair: %v", err)
		}

		// Simulate some delay and then perform operations
		time.Sleep(1 * time.Second)

		// Try to use the key (should work)
		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
		testData := []byte("session timeout test")
		hash := sha256.Sum256(testData)

		_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			t.Errorf("Signing after delay failed: %v", err)
		}

		t.Log("Session timeout handling test completed")
	})

	t.Log("Session management test completed successfully")
}

// TestE2E_SoftHSM_LargeDataProcessing tests handling of large datasets
func TestE2E_SoftHSM_LargeDataProcessing(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing large data processing with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test large data encryption with AES
	t.Run("LargeAESEncryption", func(t *testing.T) {
		// Generate AES key
		aesKey, err := client.GenerateAESKey("large-data-aes-key", 256)
		if err != nil {
			t.Fatalf("Failed to generate AES key: %v", err)
		}

		// Create large test data (1MB)
		largeData := make([]byte, 1024*1024)
		_, err = rand.Read(largeData)
		if err != nil {
			t.Fatalf("Failed to generate large test data: %v", err)
		}

		// Generate IV
		iv := make([]byte, 16)
		_, err = rand.Read(iv)
		if err != nil {
			t.Fatalf("Failed to generate IV: %v", err)
		}

		// Encrypt large data
		start := time.Now()
		ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, largeData)
		encryptTime := time.Since(start)
		if err != nil {
			t.Fatalf("Failed to encrypt large data: %v", err)
		}

		// Decrypt large data
		start = time.Now()
		decrypted, err := client.DecryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, ciphertext)
		decryptTime := time.Since(start)
		if err != nil {
			t.Fatalf("Failed to decrypt large data: %v", err)
		}

		// Verify data integrity
		if !bytes.Equal(largeData, decrypted) {
			t.Error("Large data encryption/decryption integrity check failed")
		}

		t.Logf("Large data encryption: %v, decryption: %v", encryptTime, decryptTime)
		t.Logf("Processed %d bytes successfully", len(largeData))
	})

	// Test multiple large signing operations
	t.Run("LargeSigningOperations", func(t *testing.T) {
		// Generate RSA key
		keyPair, err := client.GenerateRSAKeyPair("large-signing-key", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key pair: %v", err)
		}

		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

		// Perform many signing operations with moderately large data
		const numOperations = 100
		const dataSize = 64 * 1024 // 64KB per operation

		start := time.Now()
		for i := 0; i < numOperations; i++ {
			testData := make([]byte, dataSize)
			_, err = rand.Read(testData)
			if err != nil {
				t.Fatalf("Failed to generate test data for operation %d: %v", i, err)
			}

			hash := sha256.Sum256(testData)
			_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
			if err != nil {
				t.Fatalf("Signing operation %d failed: %v", i, err)
			}
		}
		totalTime := time.Since(start)

		t.Logf("Completed %d signing operations in %v (avg: %v per operation)",
			numOperations, totalTime, totalTime/numOperations)
	})

	t.Log("Large data processing test completed successfully")
}

// TestE2E_SoftHSM_MemoryUsage tests memory usage during long-running operations
func TestE2E_SoftHSM_MemoryUsage(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing memory usage during long-running operations with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate test keys
	aesKey, err := client.GenerateAESKey("memory-test-aes", 256)
	if err != nil {
		t.Fatalf("Failed to generate AES key: %v", err)
	}

	rsaKeyPair, err := client.GenerateRSAKeyPair("memory-test-rsa", 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	signer := yvaultpkcs11.NewPKCS11Signer(client, rsaKeyPair)

	// Measure memory before operations
	var m1, m2 runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&m1)

	// Perform memory-intensive operations
	const iterations = 1000
	testData := make([]byte, 1024) // 1KB per operation
	iv := make([]byte, 16)

	for i := 0; i < iterations; i++ {
		_, err = rand.Read(testData)
		if err != nil {
			t.Fatalf("Failed to generate test data: %v", err)
		}
		_, err = rand.Read(iv)
		if err != nil {
			t.Fatalf("Failed to generate IV: %v", err)
		}

		// AES encryption/decryption
		ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
		if err != nil {
			t.Fatalf("Encryption failed at iteration %d: %v", i, err)
		}

		_, err = client.DecryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, ciphertext)
		if err != nil {
			t.Fatalf("Decryption failed at iteration %d: %v", i, err)
		}

		// RSA signing
		hash := sha256.Sum256(testData)
		_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			t.Fatalf("Signing failed at iteration %d: %v", i, err)
		}

		// Force garbage collection every 100 iterations
		if i%100 == 0 {
			runtime.GC()
		}
	}

	// Measure memory after operations
	runtime.GC()
	runtime.ReadMemStats(&m2)

	// Calculate memory usage
	allocDiff := m2.TotalAlloc - m1.TotalAlloc
	heapDiff := int64(m2.HeapAlloc) - int64(m1.HeapAlloc)

	t.Logf("Memory usage after %d operations:", iterations)
	t.Logf("  Total allocations: %d bytes", allocDiff)
	t.Logf("  Heap difference: %d bytes", heapDiff)
	t.Logf("  Objects allocated: %d", m2.Mallocs-m1.Mallocs)
	t.Logf("  Objects freed: %d", m2.Frees-m1.Frees)

	// Basic sanity check - heap shouldn't grow excessively
	if heapDiff > 10*1024*1024 { // 10MB threshold
		t.Logf("Warning: Large heap growth detected: %d bytes", heapDiff)
	}

	t.Log("Memory usage test completed successfully")
}

// TestE2E_SoftHSM_HighConcurrencyStress tests high-concurrency scenarios
func TestE2E_SoftHSM_HighConcurrencyStress(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing high-concurrency stress scenarios with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate keys for stress testing
	rsaKeyPair, err := client.GenerateRSAKeyPair("stress-test-rsa", 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	aesKey, err := client.GenerateAESKey("stress-test-aes", 256)
	if err != nil {
		t.Fatalf("Failed to generate AES key: %v", err)
	}

	// High-concurrency signing stress test
	t.Run("HighConcurrencySigning", func(t *testing.T) {
		const numWorkers = 20
		const operationsPerWorker = 50

		var wg sync.WaitGroup
		errorChan := make(chan error, numWorkers*operationsPerWorker)

		signer := yvaultpkcs11.NewPKCS11Signer(client, rsaKeyPair)

		start := time.Now()
		for i := 0; i < numWorkers; i++ {
			wg.Add(1)
			go func(workerID int) {
				defer wg.Done()
				for j := 0; j < operationsPerWorker; j++ {
					testData := []byte(fmt.Sprintf("stress-test-worker-%d-op-%d", workerID, j))
					hash := sha256.Sum256(testData)

					_, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
					if err != nil {
						errorChan <- fmt.Errorf("worker %d operation %d failed: %v", workerID, j, err)
						return
					}
				}
			}(i)
		}

		wg.Wait()
		close(errorChan)
		duration := time.Since(start)

		// Check for errors
		var errors []error
		for err := range errorChan {
			errors = append(errors, err)
		}

		if len(errors) > 0 {
			t.Fatalf("High-concurrency signing failed with %d errors. First error: %v", len(errors), errors[0])
		}

		totalOps := numWorkers * operationsPerWorker
		t.Logf("High-concurrency signing: %d operations completed in %v (%.2f ops/sec)",
			totalOps, duration, float64(totalOps)/duration.Seconds())
	})

	// High-concurrency mixed operations stress test
	t.Run("HighConcurrencyMixedOps", func(t *testing.T) {
		const numWorkers = 15
		const operationsPerWorker = 30

		var wg sync.WaitGroup
		errorChan := make(chan error, numWorkers*operationsPerWorker)

		signer := yvaultpkcs11.NewPKCS11Signer(client, rsaKeyPair)

		start := time.Now()
		for i := 0; i < numWorkers; i++ {
			wg.Add(1)
			go func(workerID int) {
				defer wg.Done()
				for j := 0; j < operationsPerWorker; j++ {
					testData := make([]byte, 256)
					_, err := rand.Read(testData)
					if err != nil {
						errorChan <- fmt.Errorf("worker %d: failed to generate test data: %v", workerID, err)
						return
					}

					// Alternate between signing and encryption
					if j%2 == 0 {
						// RSA signing
						hash := sha256.Sum256(testData)
						_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
						if err != nil {
							errorChan <- fmt.Errorf("worker %d signing failed: %v", workerID, err)
							return
						}
					} else {
						// AES encryption
						iv := make([]byte, 16)
						_, err = rand.Read(iv)
						if err != nil {
							errorChan <- fmt.Errorf("worker %d: IV generation failed: %v", workerID, err)
							return
						}

						_, err = client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
						if err != nil {
							errorChan <- fmt.Errorf("worker %d encryption failed: %v", workerID, err)
							return
						}
					}
				}
			}(i)
		}

		wg.Wait()
		close(errorChan)
		duration := time.Since(start)

		// Check for errors
		var errors []error
		for err := range errorChan {
			errors = append(errors, err)
		}

		if len(errors) > 0 {
			t.Fatalf("High-concurrency mixed operations failed with %d errors. First error: %v", len(errors), errors[0])
		}

		totalOps := numWorkers * operationsPerWorker
		t.Logf("High-concurrency mixed operations: %d operations completed in %v (%.2f ops/sec)",
			totalOps, duration, float64(totalOps)/duration.Seconds())
	})

	t.Log("High-concurrency stress test completed successfully")
}

// TestE2E_SoftHSM_KeyLifecycleManagement tests key lifecycle operations
func TestE2E_SoftHSM_KeyLifecycleManagement(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing key lifecycle management with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test key enumeration and discovery
	t.Run("KeyEnumerationAndDiscovery", func(t *testing.T) {
		// Generate multiple keys with different types and labels
		testKeys := map[string]interface{}{
			"lifecycle-rsa-1":   "rsa",
			"lifecycle-rsa-2":   "rsa",
			"lifecycle-ecdsa-1": "ecdsa",
			"lifecycle-ecdsa-2": "ecdsa",
			"lifecycle-aes-1":   "aes",
			"lifecycle-aes-2":   "aes",
		}

		generatedKeys := make(map[string]interface{})

		// Generate all keys
		for label, keyType := range testKeys {
			switch keyType {
			case "rsa":
				key, err := client.GenerateRSAKeyPair(label, 2048)
				if err != nil {
					t.Fatalf("Failed to generate RSA key %s: %v", label, err)
				}
				generatedKeys[label] = key
			case "ecdsa":
				key, err := client.GenerateECDSAKeyPair(label, elliptic.P256())
				if err != nil {
					t.Fatalf("Failed to generate ECDSA key %s: %v", label, err)
				}
				generatedKeys[label] = key
			case "aes":
				key, err := client.GenerateAESKey(label, 256)
				if err != nil {
					t.Fatalf("Failed to generate AES key %s: %v", label, err)
				}
				generatedKeys[label] = key
			}
		}

		// Test key pair enumeration
		keyPairs, err := client.ListKeyPairs()
		if err != nil {
			t.Fatalf("Failed to list key pairs: %v", err)
		}

		// Verify all asymmetric keys are found
		asymmetricCount := 0
		for _, keyType := range testKeys {
			if keyType == "rsa" || keyType == "ecdsa" {
				asymmetricCount++
			}
		}

		if len(keyPairs) < asymmetricCount {
			t.Errorf("Expected at least %d key pairs, found %d", asymmetricCount, len(keyPairs))
		}

		// Test individual key discovery
		for label, keyType := range testKeys {
			if keyType == "rsa" || keyType == "ecdsa" {
				foundKey, err := client.FindKeyPairByLabel(label)
				if err != nil {
					t.Errorf("Failed to find key pair %s: %v", label, err)
					continue
				}
				if foundKey.Label != label {
					t.Errorf("Found key label mismatch: expected %s, got %s", label, foundKey.Label)
				}
			}
		}

		t.Logf("Key enumeration test completed - found %d key pairs", len(keyPairs))
	})

	// Test key attribute verification
	t.Run("KeyAttributeVerification", func(t *testing.T) {
		// Generate keys with specific attributes
		rsaKey, err := client.GenerateRSAKeyPair("attr-test-rsa", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		// Verify key attributes
		if rsaKey.KeyType != yvaultpkcs11.KeyPairTypeRSA {
			t.Errorf("Expected RSA key type, got %v", rsaKey.KeyType)
		}

		if rsaKey.KeySize != 2048 {
			t.Errorf("Expected 2048-bit key, got %d-bit", rsaKey.KeySize)
		}

		if rsaKey.Label != "attr-test-rsa" {
			t.Errorf("Expected label 'attr-test-rsa', got '%s'", rsaKey.Label)
		}

		// Verify public key is accessible
		if rsaKey.PublicKey == nil {
			t.Error("Public key should be accessible")
		}

		// Test ECDSA key attributes
		ecdsaKey, err := client.GenerateECDSAKeyPair("attr-test-ecdsa", elliptic.P384())
		if err != nil {
			t.Fatalf("Failed to generate ECDSA key: %v", err)
		}

		if ecdsaKey.KeyType != yvaultpkcs11.KeyPairTypeECDSA {
			t.Errorf("Expected ECDSA key type, got %v", ecdsaKey.KeyType)
		}

		if ecdsaKey.KeySize != 384 {
			t.Errorf("Expected 384-bit key, got %d-bit", ecdsaKey.KeySize)
		}

		t.Log("Key attribute verification completed successfully")
	})

	t.Log("Key lifecycle management test completed successfully")
}

// TestE2E_SoftHSM_StandardCryptoIntegration tests integration with standard Go crypto interfaces
func TestE2E_SoftHSM_StandardCryptoIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing integration with standard Go crypto interfaces")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test crypto.Signer interface compliance
	t.Run("CryptoSignerInterface", func(t *testing.T) {
		// Generate RSA key
		keyPair, err := client.GenerateRSAKeyPair("crypto-signer-test", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

		// Verify it implements crypto.Signer
		var _ crypto.Signer = signer

		// Test Public() method
		publicKey := signer.Public()
		if publicKey == nil {
			t.Error("Public() should return non-nil public key")
		}

		// Verify public key type
		rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
		if !ok {
			t.Errorf("Expected *rsa.PublicKey, got %T", publicKey)
		}

		if rsaPublicKey.Size() != 256 { // 2048 bits = 256 bytes
			t.Errorf("Expected 256-byte RSA key, got %d bytes", rsaPublicKey.Size())
		}

		// Test Sign() method with different SignerOpts
		testData := []byte("crypto.Signer interface test")
		hash := sha256.Sum256(testData)

		// Test with crypto.SHA256
		signature1, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
		if err != nil {
			t.Fatalf("Sign with crypto.SHA256 failed: %v", err)
		}

		// Test with RSA PSS options
		pssOpts := &rsa.PSSOptions{
			SaltLength: rsa.PSSSaltLengthAuto,
			Hash:       crypto.SHA256,
		}
		signature2, err := signer.Sign(rand.Reader, hash[:], pssOpts)
		if err != nil {
			t.Fatalf("Sign with PSS options failed: %v", err)
		}

		// Verify signatures
		err = rsa.VerifyPKCS1v15(rsaPublicKey, crypto.SHA256, hash[:], signature1)
		if err != nil {
			t.Errorf("PKCS1v15 signature verification failed: %v", err)
		}

		err = rsa.VerifyPSS(rsaPublicKey, crypto.SHA256, hash[:], signature2, pssOpts)
		if err != nil {
			t.Errorf("PSS signature verification failed: %v", err)
		}

		t.Log("crypto.Signer interface compliance verified")
	})

	// Test crypto.Decrypter interface compliance
	t.Run("CryptoDecrypterInterface", func(t *testing.T) {
		// Generate RSA key
		keyPair, err := client.GenerateRSAKeyPair("crypto-decrypter-test", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		rsaDecrypter, err := yvaultpkcs11.NewRSADecrypter(client, keyPair)
		if err != nil {
			t.Fatalf("Failed to create RSA decrypter: %v", err)
		}

		// Verify it implements crypto.Decrypter
		var _ crypto.Decrypter = rsaDecrypter

		// Test Public() method
		publicKey := rsaDecrypter.Public()
		rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
		if !ok {
			t.Fatalf("Expected *rsa.PublicKey, got %T", publicKey)
		}

		// Test Decrypt() method with PKCS1v15
		testData := []byte("crypto.Decrypter interface test")
		ciphertext, err := rsa.EncryptPKCS1v15(rand.Reader, rsaPublicKey, testData)
		if err != nil {
			t.Fatalf("Failed to encrypt test data: %v", err)
		}

		decrypted, err := rsaDecrypter.Decrypt(rand.Reader, ciphertext, nil)
		if err != nil {
			t.Fatalf("Decrypt with PKCS1v15 failed: %v", err)
		}

		if !bytes.Equal(testData, decrypted) {
			t.Error("Decrypted data doesn't match original")
		}

		// Test Decrypt() method with OAEP
		oaepOptions := &rsa.OAEPOptions{
			Hash: crypto.SHA256,
		}
		ciphertext2, err := rsa.EncryptOAEP(crypto.SHA256.New(), rand.Reader, rsaPublicKey, testData, nil)
		if err != nil {
			t.Fatalf("Failed to encrypt with OAEP: %v", err)
		}

		decrypted2, err := rsaDecrypter.Decrypt(rand.Reader, ciphertext2, oaepOptions)
		if err != nil {
			t.Fatalf("Decrypt with OAEP failed: %v", err)
		}

		if !bytes.Equal(testData, decrypted2) {
			t.Error("OAEP decrypted data doesn't match original")
		}

		t.Log("crypto.Decrypter interface compliance verified")
	})

	// Test hash algorithm compatibility
	t.Run("HashAlgorithmCompatibility", func(t *testing.T) {
		keyPair, err := client.GenerateRSAKeyPair("hash-compat-test", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
		publicKey := signer.Public().(*rsa.PublicKey)

		// Test all supported hash algorithms
		hashAlgorithms := []crypto.Hash{
			crypto.SHA1,
			crypto.SHA224,
			crypto.SHA256,
			crypto.SHA384,
			crypto.SHA512,
		}

		testData := []byte("hash algorithm compatibility test")

		for _, hashAlg := range hashAlgorithms {
			if !hashAlg.Available() {
				t.Logf("Hash algorithm %v not available, skipping", hashAlg)
				continue
			}

			t.Run(hashAlg.String(), func(t *testing.T) {
				hasher := hashAlg.New()
				hasher.Write(testData)
				digest := hasher.Sum(nil)

				signature, err := signer.Sign(rand.Reader, digest, hashAlg)
				if err != nil {
					t.Fatalf("Signing with %v failed: %v", hashAlg, err)
				}

				err = rsa.VerifyPKCS1v15(publicKey, hashAlg, digest, signature)
				if err != nil {
					t.Errorf("Verification with %v failed: %v", hashAlg, err)
				}

				t.Logf("Hash algorithm %v compatibility verified", hashAlg)
			})
		}
	})

	t.Log("Standard Go crypto interface integration test completed successfully")
}

// TestE2E_SoftHSM_ConfigurationValidation tests comprehensive configuration validation
func TestE2E_SoftHSM_ConfigurationValidation(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	t.Log("Testing configuration validation scenarios")

	// Test invalid library paths
	t.Run("InvalidLibraryPath", func(t *testing.T) {
		invalidConfigs := []*yvaultpkcs11.Config{
			yvaultpkcs11.NewConfig("", 0, "1234"),                    // Empty path
			yvaultpkcs11.NewConfig("/nonexistent/lib.so", 0, "1234"), // Non-existent path
			yvaultpkcs11.NewConfig("/etc/passwd", 0, "1234"),         // Wrong file type
		}

		for i, config := range invalidConfigs {
			t.Run(fmt.Sprintf("InvalidConfig%d", i), func(t *testing.T) {
				err := config.Validate()
				if err == nil {
					t.Error("Expected validation error for invalid config")
				} else {
					t.Logf("Correctly caught validation error: %v", err)
				}

				// Try to create client (should fail)
				_, err = yvaultpkcs11.NewClient(config)
				if err == nil {
					t.Error("Expected client creation to fail with invalid config")
				}
			})
		}
	})

	// Test environment variable configuration
	t.Run("EnvironmentConfiguration", func(t *testing.T) {
		// Test with missing required environment variables
		originalPin := os.Getenv("PKCS11_USER_PIN")
		os.Unsetenv("PKCS11_USER_PIN")

		_, err := yvaultpkcs11.NewConfigFromEnv()
		if err == nil {
			t.Error("Expected error when PKCS11_USER_PIN is not set")
		}

		// Restore environment
		if originalPin != "" {
			os.Setenv("PKCS11_USER_PIN", originalPin)
		}

		t.Log("Environment configuration validation completed")
	})

	t.Log("Configuration validation test completed successfully")
}
