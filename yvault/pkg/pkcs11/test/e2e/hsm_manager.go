package e2e

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"testing"

	"github.com/pkg/errors"
	"github.com/qcu266/labs/yvault/pkg/pkcs11"
)

// HSMType represents different types of HSM implementations
type HSMType int

const (
	HSMTypeSoftHSM HSMType = iota
	HSMTypeUtimaco
	HSMTypeSafeNet
	HSMTypeCloudHSM
	HSMTypeCustom
)

// String returns the string representation of HSMType
func (h HSMType) String() string {
	switch h {
	case HSMTypeSoftHSM:
		return "SoftHSM"
	case HSMTypeUtimaco:
		return "Utimaco"
	case HSMTypeSafeNet:
		return "SafeNet"
	case HSMTypeCloudHSM:
		return "CloudHSM"
	case HSMTypeCustom:
		return "Custom"
	default:
		return "Unknown"
	}
}

// HSMConfig holds configuration for a specific HSM
type HSMConfig struct {
	Type        HSMType
	Name        string
	LibraryPath string
	SlotID      uint
	UserPIN     string
	SOPIN       string
	TokenLabel  string
	Vendor      string
	Required    bool
}

// HSMManager manages multiple HSM configurations and connections
type HSMManager struct {
	configs       map[HSMType]*HSMConfig
	primary       HSMType
	fallback      []HSMType
	testDirectory string
}

// NewHSMManager creates a new HSM manager
func NewHSMManager() *HSMManager {
	return &HSMManager{
		configs:       make(map[HSMType]*HSMConfig),
		primary:       HSMTypeSoftHSM,
		fallback:      []HSMType{},
		testDirectory: os.TempDir(),
	}
}

// DiscoverHSMs automatically discovers available HSM libraries
func (hm *HSMManager) DiscoverHSMs() {
	// SoftHSM discovery (required)
	hm.discoverSoftHSM()

	// Production HSM discovery (optional)
	hm.discoverUtimaco()
	hm.discoverSafeNet()
	hm.discoverFromEnvironment()
}

// discoverSoftHSM discovers SoftHSM libraries
func (hm *HSMManager) discoverSoftHSM() {
	// Try bundled SoftHSM first
	if bundledPath, err := getBundledSoftHSMPathInternal(); err == nil {
		hm.configs[HSMTypeSoftHSM] = &HSMConfig{
			Type:        HSMTypeSoftHSM,
			Name:        "Bundled SoftHSM v2",
			LibraryPath: bundledPath,
			SlotID:      0,
			UserPIN:     "1234",
			SOPIN:       "5678",
			TokenLabel:  "TestToken",
			Vendor:      "OpenDNSSEC",
			Required:    true,
		}
		return
	}

	// Try system SoftHSM paths
	systemPaths := []string{
		"/usr/lib/softhsm/libsofthsm2.so",
		"/usr/lib/x86_64-linux-gnu/softhsm/libsofthsm2.so",
		"/usr/local/lib/softhsm/libsofthsm2.so",
		"/opt/homebrew/lib/softhsm/libsofthsm2.so",
		"/usr/lib64/pkcs11/libsofthsm2.so",
	}

	// Windows paths
	if runtime.GOOS == "windows" {
		systemPaths = append(systemPaths,
			"C:\\SoftHSM2\\lib\\softhsm2.dll",
			"C:\\Program Files\\SoftHSM2\\lib\\softhsm2.dll",
		)
	}

	for _, path := range systemPaths {
		if _, err := os.Stat(path); err == nil {
			hm.configs[HSMTypeSoftHSM] = &HSMConfig{
				Type:        HSMTypeSoftHSM,
				Name:        "System SoftHSM v2",
				LibraryPath: path,
				SlotID:      0,
				UserPIN:     "1234",
				SOPIN:       "5678",
				TokenLabel:  "TestToken",
				Vendor:      "OpenDNSSEC",
				Required:    true,
			}
			return
		}
	}
}

// discoverUtimaco discovers Utimaco HSM libraries
func (hm *HSMManager) discoverUtimaco() {
	utimacoLibs := []string{
		"/usr/lib/utimaco/libcs_pkcs11_R2.so",
		"/opt/utimaco/lib/libcs_pkcs11_R2.so",
		"/usr/local/lib/libcs_pkcs11_R2.so",
	}

	// Windows paths
	if runtime.GOOS == "windows" {
		utimacoLibs = append(utimacoLibs,
			"C:\\Program Files\\Utimaco\\CryptoServer\\lib\\cs_pkcs11_R2.dll",
		)
	}

	for _, path := range utimacoLibs {
		if _, err := os.Stat(path); err == nil {
			hm.configs[HSMTypeUtimaco] = &HSMConfig{
				Type:        HSMTypeUtimaco,
				Name:        "Utimaco CryptoServer",
				LibraryPath: path,
				SlotID:      0,
				UserPIN:     os.Getenv("UTIMACO_PIN"),
				TokenLabel:  "CryptoServer",
				Vendor:      "Utimaco",
				Required:    false,
			}
			return
		}
	}
}

// discoverSafeNet discovers SafeNet HSM libraries
func (hm *HSMManager) discoverSafeNet() {
	safenetLibs := []string{
		"/usr/lib/libeTPkcs11.so",
		"/usr/local/lib/libeTPkcs11.so",
		"/opt/safenet/lib/libeTPkcs11.so",
	}

	// Windows paths
	if runtime.GOOS == "windows" {
		safenetLibs = append(safenetLibs,
			"C:\\Program Files\\SafeNet\\LunaClient\\eTPKCS11.dll",
		)
	}

	for _, path := range safenetLibs {
		if _, err := os.Stat(path); err == nil {
			hm.configs[HSMTypeSafeNet] = &HSMConfig{
				Type:        HSMTypeSafeNet,
				Name:        "SafeNet Luna HSM",
				LibraryPath: path,
				SlotID:      0,
				UserPIN:     os.Getenv("SAFENET_PIN"),
				TokenLabel:  "Luna",
				Vendor:      "SafeNet",
				Required:    false,
			}
			return
		}
	}
}

// discoverFromEnvironment discovers HSM from environment variables
func (hm *HSMManager) discoverFromEnvironment() {
	// Check for custom HSM configuration
	if libPath := os.Getenv("PKCS11_LIBRARY_PATH"); libPath != "" {
		// Don't override SoftHSM if already configured
		if hm.configs[HSMTypeSoftHSM] != nil && strings.Contains(libPath, "softhsm") {
			hm.configs[HSMTypeSoftHSM].LibraryPath = libPath
			return
		}

		// Custom HSM configuration
		hm.configs[HSMTypeCustom] = &HSMConfig{
			Type:        HSMTypeCustom,
			Name:        "Custom HSM from Environment",
			LibraryPath: libPath,
			SlotID:      parseUintEnv("PKCS11_SLOT_ID", 0),
			UserPIN:     os.Getenv("PKCS11_USER_PIN"),
			TokenLabel:  os.Getenv("PKCS11_TOKEN_LABEL"),
			Vendor:      "Custom",
			Required:    parseRequiredEnv("PKCS11_REQUIRE_HSM"),
		}
	}

	// Check for specific production HSM environment variables
	if utimacoLib := os.Getenv("PKCS11_UTIMACO_LIBRARY"); utimacoLib != "" {
		hm.configs[HSMTypeUtimaco] = &HSMConfig{
			Type:        HSMTypeUtimaco,
			Name:        "Utimaco from Environment",
			LibraryPath: utimacoLib,
			SlotID:      parseUintEnv("PKCS11_UTIMACO_SLOT", 0),
			UserPIN:     os.Getenv("PKCS11_UTIMACO_PIN"),
			TokenLabel:  "CryptoServer",
			Vendor:      "Utimaco",
			Required:    false,
		}
	}
}

// GetPrimaryHSM returns the primary HSM configuration (SoftHSM)
func (hm *HSMManager) GetPrimaryHSM() (*HSMConfig, error) {
	if config, exists := hm.configs[hm.primary]; exists {
		return config, nil
	}
	return nil, fmt.Errorf("primary HSM %s not available", hm.primary.String())
}

// GetAvailableHSMs returns all available HSM configurations
func (hm *HSMManager) GetAvailableHSMs() map[HSMType]*HSMConfig {
	return hm.configs
}

// GetRequiredHSMs returns only required HSM configurations
func (hm *HSMManager) GetRequiredHSMs() map[HSMType]*HSMConfig {
	required := make(map[HSMType]*HSMConfig)
	for hsmType, config := range hm.configs {
		if config.Required {
			required[hsmType] = config
		}
	}
	return required
}

// GetOptionalHSMs returns only optional HSM configurations
func (hm *HSMManager) GetOptionalHSMs() map[HSMType]*HSMConfig {
	optional := make(map[HSMType]*HSMConfig)
	for hsmType, config := range hm.configs {
		if !config.Required {
			optional[hsmType] = config
		}
	}
	return optional
}

// CreatePKCS11Config creates a PKCS#11 config from HSM config
func (hc *HSMConfig) CreatePKCS11Config() *pkcs11.Config {
	return &pkcs11.Config{
		LibraryPath: hc.LibraryPath,
		SlotID:      hc.SlotID,
		UserPIN:     hc.UserPIN,
	}
}

// SetupRequiredHSMs sets up all required HSMs for testing
func SetupRequiredHSMs(t *testing.T) ([]*pkcs11.Config, func()) {
	t.Helper()

	manager := NewHSMManager()
	manager.DiscoverHSMs()

	required := manager.GetRequiredHSMs()
	if len(required) == 0 {
		t.Fatal("No required HSMs found. SoftHSM should be available.\n\nInstall SoftHSM with:\n  ./scripts/install-softhsm.sh")
	}

	var configs []*pkcs11.Config
	var cleanupFunctions []func()

	for hsmType, hsmConfig := range required {
		switch hsmType {
		case HSMTypeSoftHSM:
			// For required HSMs, create config directly since SetupSoftHSM is in softhsm_test.go
			config := hsmConfig.CreatePKCS11Config()
			configs = append(configs, config)
		default:
			// For other HSMs, create config directly
			config := hsmConfig.CreatePKCS11Config()
			configs = append(configs, config)
		}
	}

	// Combined cleanup function
	cleanup := func() {
		for _, cleanupFunc := range cleanupFunctions {
			cleanupFunc()
		}
	}

	return configs, cleanup
}

// SetupMultiHSMs sets up all available HSMs (required + optional) for testing
func SetupMultiHSMs(t *testing.T) ([]*pkcs11.Config, func()) {
	t.Helper()

	manager := NewHSMManager()
	manager.DiscoverHSMs()

	allHSMs := manager.GetAvailableHSMs()
	if len(allHSMs) == 0 {
		t.Fatal("No HSMs found. SoftHSM should be available.\n\nInstall SoftHSM with:\n  ./scripts/install-softhsm.sh")
	}

	var configs []*pkcs11.Config
	var cleanupFunctions []func()

	for hsmType, hsmConfig := range allHSMs {
		switch hsmType {
		case HSMTypeSoftHSM:
			// For SoftHSM, create config directly since SetupSoftHSM is in softhsm_test.go
			config := hsmConfig.CreatePKCS11Config()
			configs = append(configs, config)
		default:
			// For other HSMs, create config directly
			config := hsmConfig.CreatePKCS11Config()
			configs = append(configs, config)

			// Verify the HSM is actually available
			if client, err := pkcs11.NewClient(config); err == nil {
				client.Close()
				t.Logf("Optional HSM %s is available: %s", hsmType.String(), hsmConfig.LibraryPath)
			} else {
				t.Logf("Optional HSM %s is not accessible: %v", hsmType.String(), err)
				continue // Skip this HSM
			}
		}
	}

	// Combined cleanup function
	cleanup := func() {
		for _, cleanupFunc := range cleanupFunctions {
			cleanupFunc()
		}
	}

	return configs, cleanup
}

// Helper functions

func parseUintEnv(key string, defaultValue uint) uint {
	if value := os.Getenv(key); value != "" {
		if parsed, err := fmt.Sscanf(value, "%d", &defaultValue); err == nil && parsed == 1 {
			return defaultValue
		}
	}
	return defaultValue
}

func parseRequiredEnv(key string) bool {
	value := strings.ToLower(os.Getenv(key))
	return value == "true" || value == "1" || value == "yes"
}

// Diagnostic function for troubleshooting HSM issues
func DiagnoseHSMEnvironment(t *testing.T) {
	t.Helper()

	manager := NewHSMManager()
	manager.DiscoverHSMs()

	t.Log("HSM Environment Diagnosis:")
	t.Logf("Platform: %s-%s", runtime.GOOS, runtime.GOARCH)

	all := manager.GetAvailableHSMs()
	if len(all) == 0 {
		t.Log("❌ No HSMs found")
		return
	}

	for _, config := range all {
		status := "✅"
		if !config.Required {
			status = "🔧"
		}

		t.Logf("%s %s: %s", status, config.Name, config.LibraryPath)

		// Try to validate the library
		if _, err := os.Stat(config.LibraryPath); err != nil {
			t.Logf("   ❌ Library file not accessible: %v", err)
		} else {
			t.Logf("   ✅ Library file exists")
		}
	}
}

// getBundledSoftHSMPathInternal returns the path to the bundled SoftHSM library for the current platform.
func getBundledSoftHSMPathInternal() (string, error) {
	// Get current file's directory to locate the lib directory
	_, currentFile, _, ok := runtime.Caller(0)
	if !ok {
		return "", errors.New("could not determine current file path")
	}

	// Navigate to pkg/pkcs11/lib from e2e_test directory
	e2eDir := filepath.Dir(currentFile)
	testDir := filepath.Dir(e2eDir)
	pkcs11Dir := filepath.Dir(testDir)
	libDir := filepath.Join(pkcs11Dir, "lib")

	// Determine platform
	platform := runtime.GOOS + "-" + runtime.GOARCH

	// Convert Go architecture names to our naming convention
	switch runtime.GOARCH {
	case "amd64":
		// Keep as is
	case "arm64":
		// Keep as is
	default:
		return "", errors.Errorf("unsupported architecture: %s", runtime.GOARCH)
	}

	// Construct library path
	libPath := filepath.Join(libDir, platform, "libsofthsm2.so")

	// Check if file exists
	if _, err := os.Stat(libPath); os.IsNotExist(err) {
		return "", errors.Errorf("bundled SoftHSM library not found at: %s", libPath)
	}

	return libPath, nil
}
