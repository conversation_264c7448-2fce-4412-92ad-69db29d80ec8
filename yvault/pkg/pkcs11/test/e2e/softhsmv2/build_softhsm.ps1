# SoftHSMv2 Cross-Platform Build Script (PowerShell)
# This script compiles SoftHSMv2 from source and installs it to ./build directory

param(
    [string]$Version = "2.6.1",
    [switch]$Clean = $false,
    [switch]$Help = $false
)

# Configuration
$SOFTHSM_VERSION = $Version
$SOFTHSM_URL = "https://dist.opendnssec.org/source/softhsm-$SOFTHSM_VERSION.tar.gz"
$SCRIPT_DIR = Split-Path -Parent $MyInvocation.MyCommand.Path
$BUILD_DIR = Join-Path $SCRIPT_DIR "build"
$TEMP_DIR = Join-Path $SCRIPT_DIR "temp_build"
$SOURCE_DIR = Join-Path $TEMP_DIR "softhsm-$SOFTHSM_VERSION"

# Error handling
$ErrorActionPreference = "Stop"

# Logging functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Show help
function Show-Help {
    Write-Host "SoftHSMv2 Build Script for PKCS#11 Testing"
    Write-Host ""
    Write-Host "Usage: .\build_softhsm.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Version <version>  Specify SoftHSMv2 version (default: 2.6.1)"
    Write-Host "  -Clean              Clean build directory before building"
    Write-Host "  -Help               Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\build_softhsm.ps1"
    Write-Host "  .\build_softhsm.ps1 -Version 2.6.0"
    Write-Host "  .\build_softhsm.ps1 -Clean"
}

# Detect platform
function Get-Platform {
    if ($IsWindows -or $env:OS -eq "Windows_NT") {
        return "Windows"
    } elseif ($IsLinux) {
        return "Linux"
    } elseif ($IsMacOS) {
        return "Mac"
    } else {
        return "Unknown"
    }
}

# Check if running in appropriate environment
function Test-Environment {
    $platform = Get-Platform
    Write-Info "Detected platform: $platform"
    
    if ($platform -eq "Windows") {
        # Check if running in MSYS2 or have WSL
        if (-not (Get-Command "bash" -ErrorAction SilentlyContinue)) {
            Write-Error "Windows build requires MSYS2, WSL, or Git Bash"
            Write-Info "Please install one of the following:"
            Write-Info "  - MSYS2: https://www.msys2.org/"
            Write-Info "  - WSL: https://docs.microsoft.com/en-us/windows/wsl/"
            Write-Info "  - Git for Windows (includes Git Bash)"
            return $false
        }
    }
    
    return $true
}

# Download and extract source
function Get-Source {
    Write-Info "Downloading SoftHSMv2 source..."
    
    # Create temp directory
    if (Test-Path $TEMP_DIR) {
        Remove-Item $TEMP_DIR -Recurse -Force
    }
    New-Item -ItemType Directory -Path $TEMP_DIR -Force | Out-Null
    
    $tarFile = Join-Path $TEMP_DIR "softhsm-$SOFTHSM_VERSION.tar.gz"
    
    try {
        # Download using PowerShell
        Invoke-WebRequest -Uri $SOFTHSM_URL -OutFile $tarFile
        Write-Success "Source downloaded"
        
        # Extract using tar (available in Windows 10+) or 7-zip
        Push-Location $TEMP_DIR
        try {
            if (Get-Command "tar" -ErrorAction SilentlyContinue) {
                tar -xzf "softhsm-$SOFTHSM_VERSION.tar.gz"
            } elseif (Get-Command "7z" -ErrorAction SilentlyContinue) {
                7z x "softhsm-$SOFTHSM_VERSION.tar.gz"
                7z x "softhsm-$SOFTHSM_VERSION.tar"
            } else {
                throw "Neither tar nor 7-zip is available for extraction"
            }
            Write-Success "Source extracted"
        } finally {
            Pop-Location
        }
    } catch {
        Write-Error "Failed to download or extract source: $_"
        throw
    }
}

# Build using bash script
function Invoke-Build {
    Write-Info "Starting build process..."
    
    $platform = Get-Platform
    
    if ($platform -eq "Windows") {
        # Use bash to run the shell script
        $bashScript = Join-Path $SCRIPT_DIR "install-softhsmv2.sh"
        
        if (-not (Test-Path $bashScript)) {
            Write-Error "Bash script not found: $bashScript"
            Write-Info "Please ensure install-softhsmv2.sh is in the same directory"
            throw "Missing bash script"
        }
        
        # Convert Windows path to Unix-style path for bash
        $unixScriptPath = $bashScript -replace '\\', '/' -replace '^([A-Z]):', '/$1'
        
        Write-Info "Executing bash script..."
        bash -c "cd '$($SCRIPT_DIR -replace '\\', '/' -replace '^([A-Z]):', '/$1')' && ./install-softhsmv2.sh"
    } else {
        # On Unix-like systems, run the bash script directly
        $bashScript = Join-Path $SCRIPT_DIR "install-softhsmv2.sh"
        & bash $bashScript
    }
}

# Create Windows-specific setup files
function New-WindowsSetup {
    Write-Info "Creating Windows setup files..."
    
    # Create PowerShell setup script
    $setupScript = @"
# SoftHSMv2 Environment Setup Script (PowerShell)

`$SCRIPT_DIR = Split-Path -Parent `$MyInvocation.MyCommand.Path
`$PARENT_DIR = Split-Path -Parent `$SCRIPT_DIR
`$env:SOFTHSM2_CONF = Join-Path `$PARENT_DIR "softhsm2.conf"
`$env:PATH = "`$(Join-Path `$SCRIPT_DIR 'bin');`$env:PATH"

Write-Host "SoftHSMv2 environment configured" -ForegroundColor Green
Write-Host "Configuration file: `$env:SOFTHSM2_CONF"
Write-Host "Binary path: `$(Join-Path `$SCRIPT_DIR 'bin')"
Write-Host "Token directory: `$(Join-Path `$PARENT_DIR 'test_data')"
Write-Host ""
Write-Host "Usage:"
Write-Host "  . .\build\setup_env.ps1"
Write-Host "  softhsm2-util --init-token --slot 0 --label test"
"@

    $setupScriptPath = Join-Path $BUILD_DIR "setup_env.ps1"
    Set-Content -Path $setupScriptPath -Value $setupScript -Encoding UTF8
    
    Write-Success "Windows setup files created"
}

# Clean build directory
function Clear-BuildDirectory {
    if (Test-Path $BUILD_DIR) {
        Write-Info "Cleaning build directory..."
        Remove-Item $BUILD_DIR -Recurse -Force
        Write-Success "Build directory cleaned"
    }
}

# Cleanup temporary files
function Clear-TempFiles {
    if (Test-Path $TEMP_DIR) {
        Write-Info "Cleaning up temporary files..."
        Remove-Item $TEMP_DIR -Recurse -Force
        Write-Success "Cleanup completed"
    }
}

# Main function
function Main {
    try {
        if ($Help) {
            Show-Help
            return
        }
        
        Write-Info "Starting SoftHSMv2 build process for PKCS#11 testing..."
        Write-Info "Version: $SOFTHSM_VERSION"
        Write-Info "Build directory: $BUILD_DIR"
        
        if (-not (Test-Environment)) {
            throw "Environment check failed"
        }
        
        if ($Clean) {
            Clear-BuildDirectory
        }
        
        Get-Source
        Invoke-Build
        
        $platform = Get-Platform
        if ($platform -eq "Windows") {
            New-WindowsSetup
        }
        
        Write-Success "SoftHSMv2 build and installation completed!"
        Write-Info "Installation directory: $BUILD_DIR"
        Write-Info "Configuration file: $SCRIPT_DIR\softhsm2.conf"
        
        $platform = Get-Platform
        if ($platform -eq "Windows") {
            Write-Info "To use SoftHSMv2, run: . .\build\setup_env.ps1"
        } else {
            Write-Info "To use SoftHSMv2, run: source build/setup_env.sh"
        }
        
    } catch {
        Write-Error "Build failed: $_"
        exit 1
    } finally {
        Clear-TempFiles
    }
}

# Run main function
Main
