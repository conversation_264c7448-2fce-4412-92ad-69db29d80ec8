# SoftHSMv2 Build Scripts for PKCS#11 Testing

This directory contains cross-platform build scripts for compiling SoftHSMv2 from source code specifically for PKCS#11 testing in the yvault project. The compiled binaries will be installed in the `build` directory.

## Features

- **Cross-platform support**: Linux, macOS, and Windows
- **Automatic dependency checking**: Verifies required build tools
- **Parallel compilation**: Uses all available CPU cores for faster builds
- **Self-contained installation**: Installs to local `build` directory
- **Test-ready configuration**: Pre-configured for PKCS#11 testing
- **Environment setup scripts**: Easy configuration for using SoftHSMv2

## Prerequisites

### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install build-essential autoconf automake libtool pkg-config libssl-dev wget
```

### Linux (CentOS/RHEL/Fedora)
```bash
# CentOS/RHEL 7
sudo yum install gcc gcc-c++ make autoconf automake libtool pkgconfig openssl-devel wget

# CentOS/RHEL 8+ or Fedora
sudo dnf install gcc gcc-c++ make autoconf automake libtool pkgconfig openssl-devel wget
```

### macOS
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install autoconf automake libtool pkg-config openssl wget
```

### Windows
Install one of the following environments:

1. **MSYS2** (Recommended)
   - Download and install from: https://www.msys2.org/
   - Open MSYS2 terminal and run:
   ```bash
   pacman -S base-devel mingw-w64-x86_64-toolchain
   pacman -S mingw-w64-x86_64-autotools mingw-w64-x86_64-openssl
   ```

2. **WSL (Windows Subsystem for Linux)**
   - Install WSL2 from Microsoft Store
   - Follow Linux instructions above

## Usage

### Check Dependencies First

Before building, check if all required dependencies are available:

```bash
# Make script executable
chmod +x check_dependencies.sh

# Check dependencies
./check_dependencies.sh
```

### Linux and macOS

1. Make the script executable:
   ```bash
   chmod +x install-softhsmv2.sh
   ```

2. Run the build script:
   ```bash
   ./install-softhsmv2.sh
   ```

3. Set up the environment:
   ```bash
   source build/setup_env.sh
   ```

### Windows

#### Using PowerShell (Recommended)

1. Open PowerShell as Administrator (if needed)

2. Allow script execution (if not already enabled):
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. Run the build script:
   ```powershell
   .\build_softhsm.ps1
   ```

4. Set up the environment:
   ```powershell
   . .\build\setup_env.ps1
   ```

#### Using MSYS2/Git Bash

1. Open MSYS2 or Git Bash terminal

2. Run the bash script:
   ```bash
   ./install-softhsmv2.sh
   ```

3. Set up the environment:
   ```bash
   source build/setup_env.sh
   ```

## Script Options

### PowerShell Script Options

```powershell
# Build specific version
.\build_softhsm.ps1 -Version "2.6.0"

# Clean build directory before building
.\build_softhsm.ps1 -Clean

# Show help
.\build_softhsm.ps1 -Help
```

## Directory Structure

After successful build, the directory structure will be:

```
softhsmv2/
├── install-softhsmv2.sh     # Main bash build script
├── build_softhsm.ps1        # PowerShell build script
├── check_dependencies.sh    # Dependency checker
├── README.md                # This file
├── softhsm2.conf           # SoftHSMv2 configuration
├── test_data/              # Token storage directory
└── build/                  # Installation directory
    ├── bin/                # SoftHSMv2 binaries
    │   ├── softhsm2-util   # Token management utility
    │   └── ...
    ├── lib/                # Libraries
    │   ├── softhsm/        # SoftHSMv2 PKCS#11 library
    │   └── ...
    ├── etc/                # Configuration files
    ├── setup_env.sh        # Environment setup (Unix)
    ├── setup_env.bat       # Environment setup (Windows batch)
    └── setup_env.ps1       # Environment setup (Windows PowerShell)
```

## Configuration

The configuration file `softhsm2.conf` is pre-configured for testing:

```ini
# SoftHSM v2 configuration file
directories.tokendir = ./test_data/
objectstore.backend = file
log.level = INFO
slots.removable = false
```

## Using SoftHSMv2 for PKCS#11 Testing

After building and setting up the environment:

1. **Initialize a test token**:
   ```bash
   softhsm2-util --init-token --slot 0 --label "TestToken"
   ```

2. **List available tokens**:
   ```bash
   softhsm2-util --show-slots
   ```

3. **Import a test key**:
   ```bash
   softhsm2-util --import test_key.pem --slot 0 --label "TestKey" --id 01
   ```

4. **Use with Go PKCS#11 tests**:
   - The PKCS#11 library is located at: `build/lib/softhsm/libsofthsm2.so` (Linux/macOS) or `build/lib/softhsm/libsofthsm2.dll` (Windows)
   - Configuration file: `softhsm2.conf`
   - Token directory: `test_data/`

## Integration with yvault Tests

This SoftHSMv2 installation is specifically configured for the yvault PKCS#11 tests. The environment variables and paths are set up to work seamlessly with the test suite.

### Environment Variables

When using the setup scripts, the following environment variables are configured:

- `SOFTHSM2_CONF`: Points to the configuration file
- `PATH`: Includes the SoftHSMv2 binary directory
- `LD_LIBRARY_PATH` (Linux/macOS): Includes the library directory

### Test Integration

The Go tests can use this SoftHSMv2 installation by:

1. Sourcing the environment setup script
2. Using the PKCS#11 library path in test configurations
3. Utilizing the pre-configured token directory

## Troubleshooting

### Common Issues

1. **Missing dependencies**: Run `./check_dependencies.sh` to verify all required tools are installed.

2. **Permission errors**: Ensure you have write permissions in the script directory.

3. **Network issues**: If download fails, check your internet connection and firewall settings.

4. **Windows path issues**: Use forward slashes or properly escaped backslashes in paths.

### Build Logs

Build logs are displayed in real-time. If the build fails:

1. Check the error messages for missing dependencies
2. Ensure all prerequisites are installed
3. Try running with elevated privileges if needed
4. Check available disk space

### Getting Help

If you encounter issues:

1. Check the error messages carefully
2. Verify all prerequisites are installed using `./check_dependencies.sh`
3. Try building with a clean environment (`-Clean` option for PowerShell)
4. Check SoftHSMv2 official documentation: https://www.opendnssec.org/softhsm/

## License

SoftHSMv2 is licensed under the BSD 2-Clause License. See the SoftHSMv2 source code for license details.

These build scripts are provided for convenience in testing the yvault PKCS#11 implementation.

