#!/bin/bash

# Test script to validate the build script functionality without actually building
# This script tests the main functions of the build script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[TEST-INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[TEST-SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[TEST-WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[TEST-ERROR]${NC} $1"
}

# Test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="${SCRIPT_DIR}/build"
TEST_DIR="${SCRIPT_DIR}/test_build"

# Test platform detection
test_platform_detection() {
    log_info "Testing platform detection..."
    
    case "$(uname -s)" in
        Linux*)     PLATFORM=Linux;;
        Darwin*)    PLATFORM=Mac;;
        CYGWIN*|MINGW*|MSYS*) PLATFORM=Windows;;
        *)          PLATFORM="UNKNOWN";;
    esac
    
    if [ "$PLATFORM" != "UNKNOWN" ]; then
        log_success "Platform detected: $PLATFORM"
        return 0
    else
        log_error "Failed to detect platform"
        return 1
    fi
}

# Test directory creation
test_directory_creation() {
    log_info "Testing directory creation..."
    
    # Create test directory
    mkdir -p "$TEST_DIR"
    
    if [ -d "$TEST_DIR" ]; then
        log_success "Test directory created successfully"
        # Clean up
        rm -rf "$TEST_DIR"
        return 0
    else
        log_error "Failed to create test directory"
        return 1
    fi
}

# Test configuration file validation
test_config_validation() {
    log_info "Testing configuration file validation..."
    
    if [ -f "$SCRIPT_DIR/softhsm2.conf" ]; then
        log_success "Configuration file exists"
        
        # Check if config contains required settings
        if grep -q "directories.tokendir" "$SCRIPT_DIR/softhsm2.conf" && \
           grep -q "objectstore.backend" "$SCRIPT_DIR/softhsm2.conf"; then
            log_success "Configuration file contains required settings"
            return 0
        else
            log_error "Configuration file missing required settings"
            return 1
        fi
    else
        log_error "Configuration file not found"
        return 1
    fi
}

# Test script permissions
test_script_permissions() {
    log_info "Testing script permissions..."
    
    if [ -x "$SCRIPT_DIR/install-softhsmv2.sh" ]; then
        log_success "Main build script is executable"
    else
        log_error "Main build script is not executable"
        return 1
    fi
    
    if [ -x "$SCRIPT_DIR/check_dependencies.sh" ]; then
        log_success "Dependency check script is executable"
    else
        log_error "Dependency check script is not executable"
        return 1
    fi
    
    return 0
}

# Test download tools availability
test_download_tools() {
    log_info "Testing download tools availability..."
    
    if command -v wget >/dev/null 2>&1; then
        log_success "wget is available"
        return 0
    elif command -v curl >/dev/null 2>&1; then
        log_success "curl is available"
        return 0
    else
        log_error "Neither wget nor curl is available"
        return 1
    fi
}

# Test build tools availability
test_build_tools() {
    log_info "Testing build tools availability..."
    
    local missing_tools=()
    local available_tools=()
    
    # Check for compiler
    if command -v gcc >/dev/null 2>&1; then
        available_tools+=("gcc")
    elif command -v clang >/dev/null 2>&1; then
        available_tools+=("clang")
    else
        missing_tools+=("gcc/clang")
    fi
    
    # Check for make
    if command -v make >/dev/null 2>&1; then
        available_tools+=("make")
    else
        missing_tools+=("make")
    fi
    
    # Check for autotools
    for tool in autoconf automake libtool pkg-config; do
        if command -v "$tool" >/dev/null 2>&1; then
            available_tools+=("$tool")
        else
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#available_tools[@]} -gt 0 ]; then
        log_success "Available build tools: ${available_tools[*]}"
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_warning "Missing build tools: ${missing_tools[*]}"
        log_info "Run './check_dependencies.sh' for installation instructions"
        return 1
    else
        log_success "All required build tools are available"
        return 0
    fi
}

# Test script syntax
test_script_syntax() {
    log_info "Testing script syntax..."
    
    if bash -n "$SCRIPT_DIR/install-softhsmv2.sh"; then
        log_success "Main build script syntax is valid"
    else
        log_error "Main build script has syntax errors"
        return 1
    fi
    
    if bash -n "$SCRIPT_DIR/check_dependencies.sh"; then
        log_success "Dependency check script syntax is valid"
    else
        log_error "Dependency check script has syntax errors"
        return 1
    fi
    
    return 0
}

# Test PowerShell script existence
test_powershell_script() {
    log_info "Testing PowerShell script..."
    
    if [ -f "$SCRIPT_DIR/build_softhsm.ps1" ]; then
        log_success "PowerShell build script exists"
        return 0
    else
        log_warning "PowerShell build script not found"
        return 1
    fi
}

# Main test function
run_tests() {
    log_info "Starting SoftHSMv2 build script validation tests..."
    echo ""
    
    local test_count=0
    local passed_count=0
    local failed_tests=()
    
    # Run tests
    tests=(
        "test_platform_detection"
        "test_directory_creation"
        "test_config_validation"
        "test_script_permissions"
        "test_download_tools"
        "test_build_tools"
        "test_script_syntax"
        "test_powershell_script"
    )
    
    for test in "${tests[@]}"; do
        test_count=$((test_count + 1))
        echo ""
        if $test; then
            passed_count=$((passed_count + 1))
        else
            failed_tests+=("$test")
        fi
    done
    
    # Report results
    echo ""
    echo "========================================"
    log_info "Test Results Summary:"
    echo "  Total tests: $test_count"
    echo "  Passed: $passed_count"
    echo "  Failed: $((test_count - passed_count))"
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        log_success "All tests passed! The build scripts are ready to use."
        echo ""
        log_info "Next steps:"
        echo "  1. Run './check_dependencies.sh' to verify all dependencies"
        echo "  2. Run './install-softhsmv2.sh' to build SoftHSMv2"
        return 0
    else
        echo ""
        log_error "Failed tests:"
        for test in "${failed_tests[@]}"; do
            echo "  ✗ $test"
        done
        echo ""
        log_info "Please fix the issues above before running the build script."
        return 1
    fi
}

# Run the tests
run_tests
