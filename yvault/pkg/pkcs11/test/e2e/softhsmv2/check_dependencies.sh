#!/bin/bash

# Test script to check if build dependencies are available
# This script tests the dependency checking functionality without actually building

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect platform
detect_platform() {
    case "$(uname -s)" in
        Linux*)     PLATFORM=Linux;;
        Darwin*)    PLATFORM=Mac;;
        CYGWIN*|MINGW*|MSYS*) PLATFORM=Windows;;
        *)          PLATFORM="UNKNOWN";;
    esac
    log_info "Detected platform: $PLATFORM"
}

# Check dependencies
check_dependencies() {
    log_info "Checking build dependencies for SoftHSMv2..."
    
    local missing_deps=()
    local available_deps=()
    
    # Common dependencies
    if command -v gcc >/dev/null 2>&1; then
        available_deps+=("gcc")
    elif command -v clang >/dev/null 2>&1; then
        available_deps+=("clang")
    else
        missing_deps+=("gcc/clang")
    fi
    
    if command -v make >/dev/null 2>&1; then
        available_deps+=("make")
    else
        missing_deps+=("make")
    fi
    
    if command -v autoconf >/dev/null 2>&1; then
        available_deps+=("autoconf")
    else
        missing_deps+=("autoconf")
    fi
    
    if command -v automake >/dev/null 2>&1; then
        available_deps+=("automake")
    else
        missing_deps+=("automake")
    fi
    
    if command -v libtool >/dev/null 2>&1; then
        available_deps+=("libtool")
    else
        missing_deps+=("libtool")
    fi
    
    if command -v pkg-config >/dev/null 2>&1; then
        available_deps+=("pkg-config")
    else
        missing_deps+=("pkg-config")
    fi
    
    # Download tools
    if command -v wget >/dev/null 2>&1; then
        available_deps+=("wget")
    elif command -v curl >/dev/null 2>&1; then
        available_deps+=("curl")
    else
        missing_deps+=("wget/curl")
    fi
    
    # Platform-specific checks
    case $PLATFORM in
        Linux)
            # Check for OpenSSL development headers
            if pkg-config --exists openssl 2>/dev/null; then
                available_deps+=("openssl-dev")
            else
                missing_deps+=("libssl-dev/openssl-devel")
            fi
            ;;
        Mac)
            # Check for Homebrew OpenSSL
            if brew --prefix openssl >/dev/null 2>&1; then
                available_deps+=("openssl (Homebrew)")
            else
                missing_deps+=("openssl (via Homebrew)")
            fi
            ;;
        Windows)
            log_warning "Windows build requires MSYS2/MinGW environment"
            ;;
    esac
    
    # Report results
    echo ""
    log_info "Dependency Check Results:"
    echo ""
    
    if [ ${#available_deps[@]} -ne 0 ]; then
        log_success "Available dependencies:"
        for dep in "${available_deps[@]}"; do
            echo "  ✓ $dep"
        done
        echo ""
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies:"
        for dep in "${missing_deps[@]}"; do
            echo "  ✗ $dep"
        done
        echo ""
        show_install_instructions
        return 1
    else
        log_success "All required dependencies are available!"
        log_info "You can now run the build script: ./install-softhsmv2.sh"
        return 0
    fi
}

# Show installation instructions for dependencies
show_install_instructions() {
    log_info "Installation instructions for missing dependencies:"
    echo ""
    case $PLATFORM in
        Linux)
            echo "Ubuntu/Debian:"
            echo "  sudo apt-get update"
            echo "  sudo apt-get install build-essential autoconf automake libtool pkg-config libssl-dev wget"
            echo ""
            echo "CentOS/RHEL/Fedora:"
            echo "  sudo yum install gcc gcc-c++ make autoconf automake libtool pkgconfig openssl-devel wget"
            echo "  # or for newer versions:"
            echo "  sudo dnf install gcc gcc-c++ make autoconf automake libtool pkgconfig openssl-devel wget"
            ;;
        Mac)
            echo "macOS (using Homebrew):"
            echo "  # Install Homebrew if not already installed:"
            echo "  /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
            echo ""
            echo "  # Install dependencies:"
            echo "  brew install autoconf automake libtool pkg-config openssl wget"
            ;;
        Windows)
            echo "Windows (MSYS2):"
            echo "  # Download and install MSYS2 from: https://www.msys2.org/"
            echo "  # Then run in MSYS2 terminal:"
            echo "  pacman -S base-devel mingw-w64-x86_64-toolchain"
            echo "  pacman -S mingw-w64-x86_64-autotools mingw-w64-x86_64-openssl"
            ;;
    esac
}

# Main function
main() {
    log_info "SoftHSMv2 Build Dependency Checker for PKCS#11 Testing"
    echo ""
    
    detect_platform
    
    if check_dependencies; then
        exit 0
    else
        exit 1
    fi
}

# Run main function
main "$@"
