# PKCS#11 End-to-End Tests

This directory contains comprehensive end-to-end tests for the PKCS#11 package using SoftHSM.

## Test Structure

### Core Test Files

- **`e2e_test.go`** - Original comprehensive e2e tests covering:
  - RSA workflow (key generation, signing, decryption, certificates)
  - ECDSA workflow (key generation, signing)
  - Multiple key management
  - Certificate generation (self-signed, CA-signed)
  - Concurrent operations
  - AES/DES/3DES symmetric encryption
  - Key import functionality
  - Key wrapping/unwrapping
  - RSA OAEP decryption
  - Hybrid encryption (RSA + AES)
  - Real-world TLS scenarios
  - Performance benchmarks

### Enhanced Test Files

- **`e2e_enhanced_test.go`** - Enhanced e2e tests covering:
  - RSA PSS signatures with multiple hash algorithms
  - Session management and timeout handling
  - Large data processing (1MB+ datasets)
  - Memory usage testing during long-running operations
  - High-concurrency stress testing (20+ workers)
  - Key lifecycle management and enumeration
  - Standard Go crypto interface compliance
  - Configuration validation scenarios

- **`e2e_security_test.go`** - Security-focused tests covering:
  - Key non-extractability verification
  - Sensitive key handling
  - Key isolation between sessions
  - Authentication requirements
  - Data integrity and consistency
  - Error recovery scenarios
  - Performance regression testing
  - PKCS#11 compliance and standards

- **`e2e_advanced_test.go`** - Advanced scenario tests covering:
  - Advanced key operations (multiple curves, key sizes)
  - Concurrent key generation
  - Extensive symmetric key testing
  - Comprehensive hashing and signing variations
  - Edge cases and limits testing

### Support Files

- **`softhsm_test.go`** - SoftHSM test infrastructure and utilities
- **`example_test.go`** - Example usage patterns for documentation
- **`softhsm2.conf`** - SoftHSM configuration file

## Running Tests

### Quick Tests
```bash
# Run all tests in short mode (skips e2e tests)
go test -short ./...

# Run only unit tests
go test ./... -run '^Test.*' -exclude '^TestE2E_.*'
```

### Full E2E Tests
```bash
# Run all e2e tests (requires SoftHSM)
go test ./e2e_test/

# Run specific test categories
go test ./e2e_test/ -run TestE2E_SoftHSM_RSAWorkflow
go test ./e2e_test/ -run TestE2E_SoftHSM_Security
go test ./e2e_test/ -run TestE2E_SoftHSM_Advanced

# Run with race detection
go test -race ./e2e_test/

# Run with verbose output
go test -v ./e2e_test/
```

### Performance Tests
```bash
# Run benchmarks
go test -bench=. ./e2e_test/

# Run specific benchmarks
go test -bench=BenchmarkE2E_SoftHSM_RSASigning ./e2e_test/
go test -bench=BenchmarkE2E_SoftHSM_AESEncryption ./e2e_test/
```

## Prerequisites

### SoftHSM Installation
The e2e tests require SoftHSM to be available. The tests automatically look for bundled SoftHSM libraries in the `../lib/` directory.

### Environment Variables
```bash
export PKCS11_LIBRARY_PATH="/usr/lib/pkcs11/libpkcs11.so"
export PKCS11_SLOT_ID="0"
export PKCS11_USER_PIN="1234"
```

## Test Coverage

The comprehensive test suite covers:

### Cryptographic Operations
- **Asymmetric Cryptography**: RSA (2048/4096-bit), ECDSA (P-256/P-384)
- **Symmetric Cryptography**: AES (128/192/256-bit), DES, 3DES
- **Hash Functions**: SHA-1, SHA-224, SHA-256, SHA-384, SHA-512
- **Padding Schemes**: PKCS#1 v1.5, PSS, OAEP
- **Digital Signatures**: PKCS#1 v1.5, PSS with auto-hashing
- **Encryption/Decryption**: RSA, AES (CBC/ECB), DES, 3DES

### Key Management
- Key generation (asymmetric and symmetric)
- Key import and export restrictions
- Key discovery and enumeration
- Key wrapping and unwrapping
- Key lifecycle management
- Multi-session key isolation

### Security Features
- Non-extractable key verification
- Sensitive key handling
- Authentication enforcement
- Session management
- Error handling and recovery
- Data integrity validation

### Performance & Scalability
- Concurrent operations (up to 20 workers)
- Large data processing (1MB+ datasets)
- Memory usage monitoring
- Performance regression testing
- High-throughput scenarios

### Compliance & Standards
- PKCS#11 v2.x compliance
- Standard Go crypto interface compatibility
- Proper error code handling
- Security best practices validation

## Test Design Principles

1. **Isolation**: Each test is independent and doesn't affect others
2. **Cleanup**: Automatic cleanup of test tokens and temporary files
3. **Resilience**: Tests handle missing dependencies gracefully
4. **Coverage**: Comprehensive coverage of success and failure paths
5. **Performance**: Performance baselines and regression detection
6. **Security**: Security-first testing approach with validation

## Debugging Tests

### Verbose Output
```bash
go test -v ./e2e_test/ -run TestE2E_SoftHSM_SpecificTest
```

### Test with Debug Logging
```bash
SOFTHSM2_CONF=./softhsm2.conf go test -v ./e2e_test/
```

### Skip Specific Tests
```bash
go test ./e2e_test/ -skip TestE2E_SoftHSM_LargeDataProcessing
```