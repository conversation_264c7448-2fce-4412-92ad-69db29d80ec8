package e2e

import (
	"crypto"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/miekg/pkcs11"
	yvaultpkcs11 "github.com/qcu266/labs/yvault/pkg/pkcs11"
)

// TestE2E_SoftHSM_AdvancedKeyOperations tests advanced key operations
func TestE2E_SoftHSM_AdvancedKeyOperations(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing advanced key operations with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test key generation with different curve sizes
	t.Run("ECDSACurveVariations", func(t *testing.T) {
		curves := []struct {
			name  string
			curve elliptic.Curve
			bits  int
		}{
			{"P-256", elliptic.P256(), 256},
			{"P-384", elliptic.P384(), 384},
		}

		for _, tc := range curves {
			t.Run(tc.name, func(t *testing.T) {
				label := fmt.Sprintf("advanced-ecdsa-%s", tc.name)
				keyPair, err := client.GenerateECDSAKeyPair(label, tc.curve)
				if err != nil {
					t.Fatalf("Failed to generate %s key: %v", tc.name, err)
				}

				if keyPair.KeySize != tc.bits {
					t.Errorf("Expected %d-bit key, got %d-bit", tc.bits, keyPair.KeySize)
				}

				// Test signing with the curve
				signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
				testData := []byte(fmt.Sprintf("ECDSA %s test", tc.name))
				hash := sha256.Sum256(testData)

				signature, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
				if err != nil {
					t.Fatalf("Failed to sign with %s: %v", tc.name, err)
				}

				if len(signature) == 0 {
					t.Errorf("Empty signature for %s", tc.name)
				}

				t.Logf("Successfully generated and used %s ECDSA key", tc.name)
			})
		}
	})

	// Test RSA key generation with different sizes
	t.Run("RSAKeySizeVariations", func(t *testing.T) {
		keySizes := []int{2048, 4096}

		for _, keySize := range keySizes {
			t.Run(fmt.Sprintf("RSA-%d", keySize), func(t *testing.T) {
				label := fmt.Sprintf("advanced-rsa-%d", keySize)

				start := time.Now()
				keyPair, err := client.GenerateRSAKeyPair(label, keySize)
				keyGenTime := time.Since(start)

				if err != nil {
					t.Fatalf("Failed to generate RSA-%d key: %v", keySize, err)
				}

				if keyPair.KeySize != keySize {
					t.Errorf("Expected %d-bit key, got %d-bit", keySize, keyPair.KeySize)
				}

				// Test signing performance with different key sizes
				signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
				testData := []byte(fmt.Sprintf("RSA-%d performance test", keySize))
				hash := sha256.Sum256(testData)

				start = time.Now()
				signature, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
				signTime := time.Since(start)

				if err != nil {
					t.Fatalf("Failed to sign with RSA-%d: %v", keySize, err)
				}

				// Verify signature
				publicKey := signer.Public().(*rsa.PublicKey)
				err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
				if err != nil {
					t.Fatalf("Failed to verify RSA-%d signature: %v", keySize, err)
				}

				t.Logf("RSA-%d: KeyGen=%v, Sign=%v", keySize, keyGenTime, signTime)
			})
		}
	})

	t.Log("Advanced key operations test completed successfully")
}

// TestE2E_SoftHSM_ConcurrentKeyGeneration tests concurrent key generation
func TestE2E_SoftHSM_ConcurrentKeyGeneration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing concurrent key generation with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test concurrent RSA key generation
	t.Run("ConcurrentRSAGeneration", func(t *testing.T) {
		const numWorkers = 5
		const keysPerWorker = 3

		var wg sync.WaitGroup
		errorChan := make(chan error, numWorkers*keysPerWorker)
		keysChan := make(chan *yvaultpkcs11.KeyPair, numWorkers*keysPerWorker)

		start := time.Now()
		for i := 0; i < numWorkers; i++ {
			wg.Add(1)
			go func(workerID int) {
				defer wg.Done()
				for j := 0; j < keysPerWorker; j++ {
					label := fmt.Sprintf("concurrent-rsa-w%d-k%d", workerID, j)
					keyPair, err := client.GenerateRSAKeyPair(label, 2048)
					if err != nil {
						errorChan <- fmt.Errorf("worker %d key %d failed: %v", workerID, j, err)
						return
					}
					keysChan <- keyPair
				}
			}(i)
		}

		wg.Wait()
		close(errorChan)
		close(keysChan)
		totalTime := time.Since(start)

		// Check for errors
		var errors []error
		for err := range errorChan {
			errors = append(errors, err)
		}

		if len(errors) > 0 {
			t.Fatalf("Concurrent RSA generation failed with %d errors. First error: %v", len(errors), errors[0])
		}

		// Count generated keys
		var generatedKeys []*yvaultpkcs11.KeyPair
		for keyPair := range keysChan {
			generatedKeys = append(generatedKeys, keyPair)
		}

		expectedKeys := numWorkers * keysPerWorker
		if len(generatedKeys) != expectedKeys {
			t.Errorf("Expected %d keys, got %d", expectedKeys, len(generatedKeys))
		}

		t.Logf("Generated %d RSA keys concurrently in %v (avg: %v per key)",
			len(generatedKeys), totalTime, totalTime/time.Duration(len(generatedKeys)))
	})

	// Test concurrent mixed key generation
	t.Run("ConcurrentMixedGeneration", func(t *testing.T) {
		const numWorkers = 4

		var wg sync.WaitGroup
		errorChan := make(chan error, numWorkers*3) // RSA, ECDSA, AES per worker

		start := time.Now()
		for i := 0; i < numWorkers; i++ {
			wg.Add(1)
			go func(workerID int) {
				defer wg.Done()

				// Generate RSA key
				rsaLabel := fmt.Sprintf("mixed-rsa-w%d", workerID)
				_, err := client.GenerateRSAKeyPair(rsaLabel, 2048)
				if err != nil {
					errorChan <- fmt.Errorf("worker %d RSA failed: %v", workerID, err)
					return
				}

				// Generate ECDSA key
				ecdsaLabel := fmt.Sprintf("mixed-ecdsa-w%d", workerID)
				_, err = client.GenerateECDSAKeyPair(ecdsaLabel, elliptic.P256())
				if err != nil {
					errorChan <- fmt.Errorf("worker %d ECDSA failed: %v", workerID, err)
					return
				}

				// Generate AES key
				aesLabel := fmt.Sprintf("mixed-aes-w%d", workerID)
				_, err = client.GenerateAESKey(aesLabel, 256)
				if err != nil {
					errorChan <- fmt.Errorf("worker %d AES failed: %v", workerID, err)
					return
				}
			}(i)
		}

		wg.Wait()
		close(errorChan)
		totalTime := time.Since(start)

		// Check for errors
		var errors []error
		for err := range errorChan {
			errors = append(errors, err)
		}

		if len(errors) > 0 {
			t.Fatalf("Concurrent mixed generation failed with %d errors. First error: %v", len(errors), errors[0])
		}

		t.Logf("Generated %d mixed key types concurrently in %v", numWorkers*3, totalTime)
	})

	t.Log("Concurrent key generation test completed successfully")
}

// TestE2E_SoftHSM_ExtensiveSymmetricKeyTesting tests extensive symmetric key scenarios
func TestE2E_SoftHSM_ExtensiveSymmetricKeyTesting(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing extensive symmetric key scenarios with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test AES key operations with all supported mechanisms
	t.Run("AESMechanismTesting", func(t *testing.T) {
		aesKey, err := client.GenerateAESKey("extensive-aes-test", 256)
		if err != nil {
			t.Fatalf("Failed to generate AES key: %v", err)
		}

		testData := []byte("AES mechanism testing data for comprehensive coverage")

		// Test AES-CBC with padding
		t.Run("AES-CBC-PAD", func(t *testing.T) {
			iv := make([]byte, 16)
			_, err = rand.Read(iv)
			if err != nil {
				t.Fatalf("Failed to generate IV: %v", err)
			}

			ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, testData)
			if err != nil {
				t.Fatalf("AES-CBC-PAD encryption failed: %v", err)
			}

			decrypted, err := client.DecryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, ciphertext)
			if err != nil {
				t.Fatalf("AES-CBC-PAD decryption failed: %v", err)
			}

			if string(decrypted) != string(testData) {
				t.Error("AES-CBC-PAD roundtrip failed")
			}

			t.Log("AES-CBC-PAD mechanism working correctly")
		})

		// Test AES-ECB (if supported)
		t.Run("AES-ECB", func(t *testing.T) {
			// ECB doesn't use IV
			ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_ECB, nil, testData)
			if err != nil {
				t.Skipf("AES-ECB not supported: %v", err)
			}

			decrypted, err := client.DecryptData(aesKey, pkcs11.CKM_AES_ECB, nil, ciphertext)
			if err != nil {
				t.Fatalf("AES-ECB decryption failed: %v", err)
			}

			if string(decrypted) != string(testData) {
				t.Error("AES-ECB roundtrip failed")
			}

			t.Log("AES-ECB mechanism working correctly")
		})
	})

	// Test symmetric key import with validation
	t.Run("SymmetricKeyImportValidation", func(t *testing.T) {
		// Test importing known AES test vectors
		testVectors := []struct {
			name     string
			keySize  int
			keyBytes []byte
		}{
			{
				"AES-128-TestVector",
				128,
				[]byte{0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6, 0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c},
			},
			{
				"AES-256-TestVector",
				256,
				[]byte{0x60, 0x3d, 0xeb, 0x10, 0x15, 0xca, 0x71, 0xbe, 0x2b, 0x73, 0xae, 0xf0, 0x85, 0x7d, 0x77, 0x81,
					0x1f, 0x35, 0x2c, 0x07, 0x3b, 0x61, 0x08, 0xd7, 0x2d, 0x98, 0x10, 0xa3, 0x09, 0x14, 0xdf, 0xf4},
			},
		}

		for _, tv := range testVectors {
			t.Run(tv.name, func(t *testing.T) {
				importedKey, err := client.ImportAESKey(fmt.Sprintf("imported-%s", tv.name), tv.keyBytes)
				if err != nil {
					t.Fatalf("Failed to import %s: %v", tv.name, err)
				}

				if importedKey.KeySize != tv.keySize {
					t.Errorf("Expected key size %d, got %d", tv.keySize, importedKey.KeySize)
				}

				// Test the imported key with known plaintext
				testPlaintext := []byte("Hello, test vector!")
				iv := make([]byte, 16)

				ciphertext, err := client.EncryptData(importedKey, pkcs11.CKM_AES_CBC_PAD, iv, testPlaintext)
				if err != nil {
					t.Fatalf("Failed to encrypt with imported key: %v", err)
				}

				decrypted, err := client.DecryptData(importedKey, pkcs11.CKM_AES_CBC_PAD, iv, ciphertext)
				if err != nil {
					t.Fatalf("Failed to decrypt with imported key: %v", err)
				}

				if string(decrypted) != string(testPlaintext) {
					t.Errorf("Imported key roundtrip failed for %s", tv.name)
				}

				t.Logf("Successfully validated imported %s", tv.name)
			})
		}
	})

	t.Log("Extensive symmetric key testing completed successfully")
}

// TestE2E_SoftHSM_HashingAndSigningVariations tests comprehensive hashing and signing
func TestE2E_SoftHSM_HashingAndSigningVariations(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing comprehensive hashing and signing variations with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Generate test key
	keyPair, err := client.GenerateRSAKeyPair("hashing-variations-key", 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)
	publicKey := signer.Public().(*rsa.PublicKey)

	// Test all hash algorithm combinations
	t.Run("AllHashAlgorithms", func(t *testing.T) {
		hashTests := []struct {
			name string
			hash crypto.Hash
		}{
			{"SHA1", crypto.SHA1},
			{"SHA224", crypto.SHA224},
			{"SHA256", crypto.SHA256},
			{"SHA384", crypto.SHA384},
			{"SHA512", crypto.SHA512},
		}

		for _, ht := range hashTests {
			t.Run(ht.name, func(t *testing.T) {
				if !ht.hash.Available() {
					t.Skipf("Hash algorithm %s not available", ht.name)
				}

				testData := []byte(fmt.Sprintf("Hash algorithm test for %s", ht.name))
				hasher := ht.hash.New()
				hasher.Write(testData)
				digest := hasher.Sum(nil)

				// Test signing with pre-computed hash
				signature, err := signer.Sign(rand.Reader, digest, ht.hash)
				if err != nil {
					t.Fatalf("Signing with %s failed: %v", ht.name, err)
				}

				// Verify signature
				err = rsa.VerifyPKCS1v15(publicKey, ht.hash, digest, signature)
				if err != nil {
					t.Fatalf("Signature verification with %s failed: %v", ht.name, err)
				}

				// Test with HashingSigner if available
				hashingSigner, err := client.GetHashingSigner(keyPair.Label, ht.hash)
				if err != nil {
					t.Logf("HashingSigner not available for %s: %v", ht.name, err)
				} else {
					autoSignature, err := hashingSigner.Sign(rand.Reader, testData, ht.hash)
					if err != nil {
						t.Fatalf("Auto-hashing signature with %s failed: %v", ht.name, err)
					}

					// Verify auto-hashed signature
					err = rsa.VerifyPKCS1v15(publicKey, ht.hash, digest, autoSignature)
					if err != nil {
						t.Fatalf("Auto-hashed signature verification with %s failed: %v", ht.name, err)
					}

					t.Logf("Both manual and auto-hashing work for %s", ht.name)
				}

				t.Logf("Hash algorithm %s test completed successfully", ht.name)
			})
		}
	})

	// Test signature determinism and randomness
	t.Run("SignatureRandomness", func(t *testing.T) {
		testData := []byte("signature randomness test")
		hash := sha256.Sum256(testData)

		const numSignatures = 20
		signatures := make([][]byte, numSignatures)

		// Generate multiple signatures
		for i := 0; i < numSignatures; i++ {
			sig, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
			if err != nil {
				t.Fatalf("Signature %d failed: %v", i, err)
			}
			signatures[i] = sig

			// Verify each signature
			err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], sig)
			if err != nil {
				t.Fatalf("Signature %d verification failed: %v", i, err)
			}
		}

		// Check that signatures are different (they should be due to random padding)
		uniqueSignatures := make(map[string]bool)
		for i, sig := range signatures {
			sigStr := string(sig)
			if uniqueSignatures[sigStr] {
				t.Errorf("Signature %d is duplicate (signatures should be randomized)", i)
			}
			uniqueSignatures[sigStr] = true
		}

		t.Logf("Generated %d unique signatures from same data", len(uniqueSignatures))
	})

	t.Log("Comprehensive hashing and signing variations test completed successfully")
}

// TestE2E_SoftHSM_EdgeCasesAndLimits tests edge cases and limits
func TestE2E_SoftHSM_EdgeCasesAndLimits(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping e2e test in short mode")
	}

	SkipIfSoftHSMUnavailable(t)

	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	t.Log("Testing edge cases and limits with SoftHSM")

	client, err := yvaultpkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	// Test empty and minimal data operations
	t.Run("MinimalDataOperations", func(t *testing.T) {
		aesKey, err := client.GenerateAESKey("minimal-data-test", 256)
		if err != nil {
			t.Fatalf("Failed to generate AES key: %v", err)
		}

		iv := make([]byte, 16)
		_, err = rand.Read(iv)
		if err != nil {
			t.Fatalf("Failed to generate IV: %v", err)
		}

		// Test with 1-byte data
		oneByteData := []byte{0x42}
		ciphertext, err := client.EncryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, oneByteData)
		if err != nil {
			t.Fatalf("Failed to encrypt 1-byte data: %v", err)
		}

		decrypted, err := client.DecryptData(aesKey, pkcs11.CKM_AES_CBC_PAD, iv, ciphertext)
		if err != nil {
			t.Fatalf("Failed to decrypt 1-byte data: %v", err)
		}

		if len(decrypted) != 1 || decrypted[0] != 0x42 {
			t.Errorf("1-byte data roundtrip failed: expected [0x42], got %v", decrypted)
		}

		t.Log("Minimal data operations test completed")
	})

	// Test maximum label lengths
	t.Run("MaximumLabelLengths", func(t *testing.T) {
		// Test with very long label
		longLabel := string(make([]byte, 200)) // Fill with zero bytes initially
		for i := range longLabel {
			longLabel = longLabel[:i] + "A" + longLabel[i+1:]
		}

		_, err := client.GenerateAESKey(longLabel, 256)
		if err != nil {
			t.Logf("Long label rejected (expected): %v", err)
		} else {
			t.Log("Long label accepted by implementation")
		}

		// Test with label containing special characters
		specialLabel := "test-key-with-special-chars-!@#$%^&*()_+-=[]{}|;:,.<>?"
		_, err = client.GenerateAESKey(specialLabel, 256)
		if err != nil {
			t.Logf("Special character label rejected: %v", err)
		} else {
			t.Log("Special character label accepted")
		}

		t.Log("Label length and character testing completed")
	})

	// Test rapid consecutive operations
	t.Run("RapidConsecutiveOperations", func(t *testing.T) {
		keyPair, err := client.GenerateRSAKeyPair("rapid-ops-test", 2048)
		if err != nil {
			t.Fatalf("Failed to generate RSA key: %v", err)
		}

		signer := yvaultpkcs11.NewPKCS11Signer(client, keyPair)

		// Perform rapid signing operations
		const rapidOps = 50
		start := time.Now()

		for i := 0; i < rapidOps; i++ {
			testData := []byte(fmt.Sprintf("rapid operation %d", i))
			hash := sha256.Sum256(testData)

			_, err = signer.Sign(rand.Reader, hash[:], crypto.SHA256)
			if err != nil {
				t.Fatalf("Rapid operation %d failed: %v", i, err)
			}
		}

		duration := time.Since(start)
		opsPerSecond := float64(rapidOps) / duration.Seconds()

		t.Logf("Completed %d rapid operations in %v (%.2f ops/sec)",
			rapidOps, duration, opsPerSecond)
	})

	t.Log("Edge cases and limits test completed successfully")
}
