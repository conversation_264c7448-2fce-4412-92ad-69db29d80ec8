package pkcs11

import (
	"crypto"
	"crypto/rsa"
	"io"
	"math/big"

	"github.com/miekg/pkcs11"
	"github.com/pkg/errors"
)

// PKCS11Signer implements the crypto.Signer interface for PKCS#11 devices.
// It performs digital signatures using keys stored in the HSM.
type PKCS11Signer struct {
	client  *Client
	keyPair *KeyPair
}

// NewPKCS11Signer creates a new PKCS11Signer for the given key pair.
func NewPKCS11Signer(client *Client, keyPair *KeyPair) *PKCS11Signer {
	return &PKCS11Signer{
		client:  client,
		keyPair: keyPair,
	}
}

// Public returns the public key corresponding to the private key used for signing.
func (h *PKCS11Signer) Public() crypto.PublicKey {
	return h.keyPair.PublicKey
}

// Sign signs the provided digest using the private key stored in the HSM.
// The digest parameter should be the already-hashed data.
// For RSA keys, supports PKCS#1 v1.5 and PSS padding schemes.
// For ECDSA keys, signatures are converted to DER format.
func (h *PKCS11Signer) Sign(rand io.Reader, digest []byte, opts crypto.SignerOpts) ([]byte, error) {
	session, err := h.client.GetSession()
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	var mechanism *pkcs11.Mechanism
	var expectedDigestLen int

	switch h.keyPair.KeyType {
	case KeyPairTypeRSA:
		mechanism, expectedDigestLen, err = h.getRSASignMechanism(opts)
		if err != nil {
			return nil, err
		}
	case KeyPairTypeECDSA:
		mechanism, expectedDigestLen, err = h.getECDSASignMechanism(opts)
		if err != nil {
			return nil, err
		}
	default:
		return nil, errors.New("unsupported key type for signing")
	}

	if len(digest) != expectedDigestLen {
		return nil, errors.Errorf("digest length mismatch: expected %d, got %d", expectedDigestLen, len(digest))
	}

	if err := h.client.ctx.SignInit(session, []*pkcs11.Mechanism{mechanism}, h.keyPair.Handle); err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	signature, err := h.client.ctx.Sign(session, digest)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	if h.keyPair.KeyType == KeyPairTypeECDSA {
		signature, err = h.convertECDSASignatureToDER(signature)
		if err != nil {
			return nil, errors.Wrap(err, "failed to convert ECDSA signature to DER format")
		}
	}

	return signature, nil
}

// getRSASignMechanism determines the appropriate PKCS#11 mechanism for RSA signing
// based on the hash function and signing options (PKCS#1 v1.5 or PSS).
func (h *PKCS11Signer) getRSASignMechanism(opts crypto.SignerOpts) (*pkcs11.Mechanism, int, error) {
	var mechanism uint
	var expectedDigestLen int

	switch opts.HashFunc() {
	case crypto.SHA1:
		mechanism = pkcs11.CKM_SHA1_RSA_PKCS
		expectedDigestLen = 20
	case crypto.SHA224:
		mechanism = pkcs11.CKM_SHA224_RSA_PKCS
		expectedDigestLen = 28
	case crypto.SHA256:
		mechanism = pkcs11.CKM_SHA256_RSA_PKCS
		expectedDigestLen = 32
	case crypto.SHA384:
		mechanism = pkcs11.CKM_SHA384_RSA_PKCS
		expectedDigestLen = 48
	case crypto.SHA512:
		mechanism = pkcs11.CKM_SHA512_RSA_PKCS
		expectedDigestLen = 64
	default:
		if pssOpts, ok := opts.(*rsa.PSSOptions); ok {
			switch pssOpts.Hash {
			case crypto.SHA256:
				mechanism = pkcs11.CKM_SHA256_RSA_PKCS_PSS
				expectedDigestLen = 32
			case crypto.SHA384:
				mechanism = pkcs11.CKM_SHA384_RSA_PKCS_PSS
				expectedDigestLen = 48
			case crypto.SHA512:
				mechanism = pkcs11.CKM_SHA512_RSA_PKCS_PSS
				expectedDigestLen = 64
			default:
				return nil, 0, errors.New("unsupported hash function for RSA-PSS")
			}
		} else {
			return nil, 0, errors.New("unsupported hash function for RSA")
		}
	}

	return pkcs11.NewMechanism(mechanism, nil), expectedDigestLen, nil
}

// getECDSASignMechanism determines the appropriate PKCS#11 mechanism for ECDSA signing
// and validates the hash function. ECDSA uses CKM_ECDSA mechanism for all hash types.
func (h *PKCS11Signer) getECDSASignMechanism(opts crypto.SignerOpts) (*pkcs11.Mechanism, int, error) {
	var expectedDigestLen int

	switch opts.HashFunc() {
	case crypto.SHA1:
		expectedDigestLen = 20
	case crypto.SHA224:
		expectedDigestLen = 28
	case crypto.SHA256:
		expectedDigestLen = 32
	case crypto.SHA384:
		expectedDigestLen = 48
	case crypto.SHA512:
		expectedDigestLen = 64
	default:
		return nil, 0, errors.New("unsupported hash function for ECDSA")
	}

	return pkcs11.NewMechanism(pkcs11.CKM_ECDSA, nil), expectedDigestLen, nil
}

// convertECDSASignatureToDER converts an ECDSA signature from the raw format
// returned by PKCS#11 (r||s) to DER encoding as expected by Go's crypto interfaces.
func (h *PKCS11Signer) convertECDSASignatureToDER(signature []byte) ([]byte, error) {
	if len(signature)%2 != 0 {
		return nil, errors.New("invalid ECDSA signature length")
	}

	halfLen := len(signature) / 2
	r := new(big.Int).SetBytes(signature[:halfLen])
	s := new(big.Int).SetBytes(signature[halfLen:])

	rBytes := r.Bytes()
	sBytes := s.Bytes()

	if len(rBytes) == 0 || len(sBytes) == 0 {
		return nil, errors.New("invalid ECDSA signature components")
	}

	rLen := len(rBytes)
	sLen := len(sBytes)

	if rBytes[0] >= 0x80 {
		rLen++
	}
	if sBytes[0] >= 0x80 {
		sLen++
	}

	totalLen := 4 + rLen + sLen
	if totalLen >= 0x80 {
		totalLen++
	}

	der := make([]byte, 0, totalLen+2)
	der = append(der, 0x30)

	if totalLen-2 >= 0x80 {
		der = append(der, 0x81, byte(totalLen-3))
	} else {
		der = append(der, byte(totalLen-2))
	}

	der = append(der, 0x02)
	if rBytes[0] >= 0x80 {
		der = append(der, byte(len(rBytes)+1), 0x00)
	} else {
		der = append(der, byte(len(rBytes)))
	}
	der = append(der, rBytes...)

	der = append(der, 0x02)
	if sBytes[0] >= 0x80 {
		der = append(der, byte(len(sBytes)+1), 0x00)
	} else {
		der = append(der, byte(len(sBytes)))
	}
	der = append(der, sBytes...)

	return der, nil
}

// HashingSigner wraps a crypto.Signer to automatically hash data before signing.
// This is useful when you want to sign raw data rather than pre-computed digests.
type HashingSigner struct {
	signer crypto.Signer
	hash   crypto.Hash
}

// NewHashingSigner creates a new HashingSigner that will hash data with the specified
// hash function before passing it to the underlying signer.
func NewHashingSigner(signer crypto.Signer, hashFunc crypto.Hash) *HashingSigner {
	return &HashingSigner{
		signer: signer,
		hash:   hashFunc,
	}
}

// Public returns the public key corresponding to the private key used for signing.
func (h *HashingSigner) Public() crypto.PublicKey {
	return h.signer.Public()
}

// Sign hashes the provided data with the configured hash function and then signs the digest.
// This is convenient for signing raw data without manually computing the hash first.
func (h *HashingSigner) Sign(rand io.Reader, data []byte, opts crypto.SignerOpts) ([]byte, error) {
	hasher := h.hash.New()
	hasher.Write(data)
	digest := hasher.Sum(nil)

	if opts == nil {
		opts = h.hash
	}

	return h.signer.Sign(rand, digest, opts)
}

// Hash returns the hash function used by this HashingSigner.
func (h *HashingSigner) Hash() crypto.Hash {
	return h.hash
}

// signerOpts implements crypto.SignerOpts for specifying hash functions.
type signerOpts struct {
	hashFunc crypto.Hash
}

func (s signerOpts) HashFunc() crypto.Hash {
	return s.hashFunc
}

// NewSignerOpts creates a new crypto.SignerOpts with the specified hash function.
func NewSignerOpts(hashFunc crypto.Hash) crypto.SignerOpts {
	return signerOpts{hashFunc: hashFunc}
}

// GetSigner returns a crypto.Signer for the key pair with the specified label.
// The returned signer expects pre-computed digest data.
func (c *Client) GetSigner(keyLabel string) (crypto.Signer, error) {
	keyPair, err := c.FindKeyPairByLabel(keyLabel)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to find key with label: %s", keyLabel)
	}

	return NewPKCS11Signer(c, keyPair), nil
}

// GetSignerByID returns a crypto.Signer for the key pair with the specified ID.
// The returned signer expects pre-computed digest data.
func (c *Client) GetSignerByID(keyID []byte) (crypto.Signer, error) {
	keyPair, err := c.FindKeyPairByID(keyID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to find key by ID")
	}

	return NewPKCS11Signer(c, keyPair), nil
}

// GetHashingSigner returns a crypto.Signer that automatically hashes data before signing.
// This is convenient when you want to sign raw data rather than pre-computed digests.
func (c *Client) GetHashingSigner(keyLabel string, hashFunc crypto.Hash) (crypto.Signer, error) {
	signer, err := c.GetSigner(keyLabel)
	if err != nil {
		return nil, err
	}

	return NewHashingSigner(signer, hashFunc), nil
}
