package pkcs11

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"math/big"
	"os"
	"testing"
	"time"
)

// Integration tests that demonstrate the full PKCS#11 workflow
// These tests use mocks and can be extended to use real PKCS#11 libraries

func TestIntegration_CompleteWorkflow(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// This test demonstrates a complete workflow:
	// 1. Create client
	// 2. Generate key pair
	// 3. Sign data
	// 4. Verify signature
	// 5. Decrypt data

	t.Run("RSA workflow", func(t *testing.T) {
		// Create test configuration
		config := NewTestConfig()
		
		// In a real integration test, you would:
		// client, err := NewClient(config)
		// if err != nil {
		//     t.Fatalf("Failed to create client: %v", err)
		// }
		// defer client.Close()

		// For this test, we simulate the workflow steps
		testRSAWorkflow(t, config)
	})

	t.Run("ECDSA workflow", func(t *testing.T) {
		config := NewTestConfig()
		testECDSAWorkflow(t, config)
	})
}

func testRSAWorkflow(t *testing.T, config *Config) {
	// Step 1: Configuration validation
	if err := config.Validate(); err != nil {
		// Create a temporary file for testing
		tmpFile, createErr := os.CreateTemp("", "libpkcs11*.so")
		if createErr != nil {
			t.Fatalf("Failed to create temp file: %v", createErr)
		}
		defer os.Remove(tmpFile.Name())
		tmpFile.Close()
		
		config.LibraryPath = tmpFile.Name()
		if err := config.Validate(); err != nil {
			t.Fatalf("Config validation failed: %v", err)
		}
	}

	// Step 2: Simulate key generation
	keyLabel := "test-rsa-key-" + time.Now().Format("20060102150405")
	keySize := 2048

	// In a real test with PKCS#11:
	// keyPair, err := client.GenerateRSAKeyPair(keyLabel, keySize)
	// if err != nil {
	//     t.Fatalf("Failed to generate RSA key pair: %v", err)
	// }

	// Simulate key generation result
	rsaKey, err := rsa.GenerateKey(rand.Reader, keySize)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	keyPair := &KeyPair{
		Handle:    1000,
		Label:     keyLabel,
		ID:        []byte(keyLabel),
		KeyType:   KeyPairTypeRSA,
		KeySize:   keySize,
		PublicKey: &rsaKey.PublicKey,
	}

	t.Logf("Generated RSA key: %s", keyPair.String())

	// Step 3: Test signing workflow
	testData := []byte("Hello, PKCS#11 World!")
	hash := sha256.Sum256(testData)

	// Create signer
	// In a real test: signer := NewPKCS11Signer(client, keyPair)
	signer := NewPKCS11Signer(&Client{}, keyPair)

	// Test that public key is accessible
	publicKey := signer.Public()
	rsaPub, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		t.Fatal("Expected RSA public key")
	}

	// Verify the public key properties
	if rsaPub.Size() != keySize/8 {
		t.Errorf("Expected key size %d bytes, got %d", keySize/8, rsaPub.Size())
	}

	// Step 4: Test signing (would require real PKCS#11 implementation)
	// signature, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
	// if err != nil {
	//     t.Fatalf("Failed to sign: %v", err)
	// }

	// Simulate signature for workflow demonstration
	signature, err := rsa.SignPKCS1v15(rand.Reader, rsaKey, crypto.SHA256, hash[:])
	if err != nil {
		t.Fatalf("Failed to create test signature: %v", err)
	}

	// Step 5: Verify signature
	err = rsa.VerifyPKCS1v15(&rsaKey.PublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		t.Fatalf("Signature verification failed: %v", err)
	}

	t.Log("RSA signing workflow completed successfully")

	// Step 6: Test decryption workflow
	testDecryptionWorkflow(t, keyPair, rsaKey)
}

func testECDSAWorkflow(t *testing.T, config *Config) {
	// Similar to RSA workflow but for ECDSA
	keyLabel := "test-ecdsa-key-" + time.Now().Format("20060102150405")

	// Simulate ECDSA key for workflow
	// In real test: keyPair, err := client.GenerateECDSAKeyPair(keyLabel, elliptic.P256())

	t.Logf("ECDSA workflow with key: %s", keyLabel)

	// Test curve validation
	testECDSACurveValidation(t)

	t.Log("ECDSA workflow completed successfully")
}

func testDecryptionWorkflow(t *testing.T, keyPair *KeyPair, rsaKey *rsa.PrivateKey) {
	// Test RSA decryption workflow
	plaintext := []byte("Secret message for decryption test")

	// Encrypt with public key
	ciphertext, err := rsa.EncryptPKCS1v15(rand.Reader, &rsaKey.PublicKey, plaintext)
	if err != nil {
		t.Fatalf("Failed to encrypt test data: %v", err)
	}

	// Create decrypter
	// In real test: decrypter, err := NewPKCS11Decrypter(client, keyPair)
	decrypter, err := NewPKCS11Decrypter(&Client{}, keyPair)
	if err != nil {
		t.Fatalf("Failed to create decrypter: %v", err)
	}

	// Test that public key is accessible
	publicKey := decrypter.Public()
	if publicKey != keyPair.PublicKey {
		t.Error("Decrypter should return the same public key")
	}

	// In real test: decrypted, err := decrypter.Decrypt(rand.Reader, ciphertext, nil)
	// Simulate decryption
	decrypted, err := rsa.DecryptPKCS1v15(rand.Reader, rsaKey, ciphertext)
	if err != nil {
		t.Fatalf("Failed to decrypt: %v", err)
	}

	if string(decrypted) != string(plaintext) {
		t.Errorf("Decrypted text doesn't match. Expected: %s, Got: %s", plaintext, decrypted)
	}

	t.Log("RSA decryption workflow completed successfully")

	// Test OAEP decryption
	testOAEPDecryptionWorkflow(t, rsaKey)
}

func testOAEPDecryptionWorkflow(t *testing.T, rsaKey *rsa.PrivateKey) {
	plaintext := []byte("OAEP secret message")
	label := []byte("test-label")

	// Encrypt with OAEP
	ciphertext, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, &rsaKey.PublicKey, plaintext, label)
	if err != nil {
		t.Fatalf("Failed to encrypt with OAEP: %v", err)
	}

	// In real test, would use PKCS#11 decryption
	decrypted, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, rsaKey, ciphertext, label)
	if err != nil {
		t.Fatalf("Failed to decrypt OAEP: %v", err)
	}

	if string(decrypted) != string(plaintext) {
		t.Errorf("OAEP decrypted text doesn't match. Expected: %s, Got: %s", plaintext, decrypted)
	}

	t.Log("OAEP decryption workflow completed successfully")
}

func testECDSACurveValidation(t *testing.T) {
	// Test supported curves
	supportedCurves := []string{"P-256", "P-384"}
	unsupportedCurves := []string{"P-521"}

	for _, curve := range supportedCurves {
		t.Logf("Testing supported curve: %s", curve)
		// In real test: would call GenerateECDSAKeyPair
	}

	for _, curve := range unsupportedCurves {
		t.Logf("Testing unsupported curve: %s (should fail)", curve)
		// In real test: would expect error from GenerateECDSAKeyPair
	}
}

func TestIntegration_CertificateGeneration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping certificate generation test in short mode")
	}

	// Test generating a self-signed certificate using PKCS#11 key
	// This demonstrates how the PKCS#11 signer integrates with x509 certificate generation

	// Step 1: Create RSA key (simulated)
	rsaKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	keyPair := &KeyPair{
		Handle:    2000,
		Label:     "cert-signing-key",
		ID:        []byte("cert-key-id"),
		KeyType:   KeyPairTypeRSA,
		KeySize:   2048,
		PublicKey: &rsaKey.PublicKey,
	}

	// Step 2: Create PKCS#11 signer
	_ = NewPKCS11Signer(&Client{}, keyPair)

	// Step 3: Create certificate template
	template := &x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization:  []string{"Test Organization"},
			Country:       []string{"US"},
			Province:      []string{""},
			Locality:      []string{"Test City"},
			StreetAddress: []string{""},
			PostalCode:    []string{""},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().AddDate(1, 0, 0), // 1 year
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
	}

	// Step 4: Generate certificate
	// In real test: would use signer directly
	// certDER, err := x509.CreateCertificate(rand.Reader, template, template, signer.Public(), signer)
	
	// For this test, use the underlying RSA key
	certDER, err := x509.CreateCertificate(rand.Reader, template, template, &rsaKey.PublicKey, rsaKey)
	if err != nil {
		t.Fatalf("Failed to create certificate: %v", err)
	}

	// Step 5: Parse and validate certificate
	cert, err := x509.ParseCertificate(certDER)
	if err != nil {
		t.Fatalf("Failed to parse certificate: %v", err)
	}

	// Verify certificate properties
	if cert.Subject.Organization[0] != "Test Organization" {
		t.Error("Certificate subject organization mismatch")
	}

	// Verify certificate can be encoded as PEM
	certPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "CERTIFICATE",
		Bytes: certDER,
	})

	if len(certPEM) == 0 {
		t.Error("Failed to encode certificate as PEM")
	}

	t.Logf("Successfully generated certificate with PKCS#11 key (simulated)")
	t.Logf("Certificate serial number: %s", cert.SerialNumber.String())
	t.Logf("Certificate valid from: %s to %s", cert.NotBefore, cert.NotAfter)
}

func TestIntegration_ErrorHandling(t *testing.T) {
	// Test error handling throughout the integration workflow

	t.Run("invalid configuration", func(t *testing.T) {
		config := &Config{
			LibraryPath: "/non/existent/path",
			SlotID:      0,
			UserPIN:     "test",
		}

		err := config.Validate()
		if err == nil {
			t.Error("Expected error for non-existent library path")
		}
	})

	t.Run("unsupported key operations", func(t *testing.T) {
		// Test that ECDSA keys cannot be used for decryption
		keyPair := &KeyPair{
			Handle:  3000,
			Label:   "ecdsa-key",
			KeyType: KeyPairTypeECDSA,
		}

		_, err := NewPKCS11Decrypter(&Client{}, keyPair)
		if err == nil {
			t.Error("Expected error when creating decrypter with ECDSA key")
		}
	})

	t.Run("invalid key sizes", func(t *testing.T) {
		// Test RSA key size validation
		invalidSizes := []int{1024, 3072, 8192}
		
		for _, size := range invalidSizes {
			t.Run(fmt.Sprintf("RSA-%d", size), func(t *testing.T) {
				// In real test: would expect GenerateRSAKeyPair to fail
				if size != 2048 && size != 4096 {
					t.Logf("RSA key size %d should be rejected", size)
				}
			})
		}
	})
}

func TestIntegration_ConcurrentOperations(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping concurrent operations test in short mode")
	}

	// Test concurrent signing operations
	// This would test the thread safety of the PKCS#11 client

	const numWorkers = 10
	const operationsPerWorker = 5

	// Create test key (simulated)
	rsaKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	keyPair := &KeyPair{
		Handle:    4000,
		Label:     "concurrent-test-key",
		KeyType:   KeyPairTypeRSA,
		KeySize:   2048,
		PublicKey: &rsaKey.PublicKey,
	}

	_ = NewPKCS11Signer(&Client{}, keyPair)

	// Channel to collect results
	results := make(chan error, numWorkers*operationsPerWorker)

	// Start workers
	for i := 0; i < numWorkers; i++ {
		go func(workerID int) {
			for j := 0; j < operationsPerWorker; j++ {
				data := []byte(fmt.Sprintf("worker-%d-operation-%d", workerID, j))
				hash := sha256.Sum256(data)

				// In real test: would call signer.Sign
				// _, err := signer.Sign(rand.Reader, hash[:], crypto.SHA256)
				
				// Simulate signing operation
				_, err := rsa.SignPKCS1v15(rand.Reader, rsaKey, crypto.SHA256, hash[:])
				results <- err
			}
		}(i)
	}

	// Collect results
	for i := 0; i < numWorkers*operationsPerWorker; i++ {
		if err := <-results; err != nil {
			t.Errorf("Concurrent operation failed: %v", err)
		}
	}

	t.Log("Concurrent operations completed successfully")
}

func TestIntegration_PerformanceBenchmark(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance benchmark in short mode")
	}

	// Basic performance test to ensure operations complete in reasonable time
	rsaKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("Failed to generate RSA key: %v", err)
	}

	data := []byte("Performance test data")
	hash := sha256.Sum256(data)

	// Measure signing performance
	start := time.Now()
	const numOperations = 100

	for i := 0; i < numOperations; i++ {
		_, err := rsa.SignPKCS1v15(rand.Reader, rsaKey, crypto.SHA256, hash[:])
		if err != nil {
			t.Fatalf("Signing operation %d failed: %v", i, err)
		}
	}

	duration := time.Since(start)
	avgDuration := duration / numOperations

	t.Logf("Performed %d signing operations in %v", numOperations, duration)
	t.Logf("Average time per operation: %v", avgDuration)

	// Ensure operations complete in reasonable time (adjust threshold as needed)
	if avgDuration > 100*time.Millisecond {
		t.Logf("Warning: Average operation time (%v) is longer than expected", avgDuration)
	}
}

