package pkcs11

import (
	"crypto"
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rsa"
	"crypto/sha256"
	"fmt"
	"math/big"

	"github.com/miekg/pkcs11"
	"github.com/pkg/errors"
)

// KeyPairType represents the type of asymmetric key pair.
type KeyPairType int

const (
	// KeyPairTypeRSA represents RSA key pairs for signing and encryption
	KeyPairTypeRSA KeyPairType = iota
	// KeyPairTypeECDSA represents ECDSA key pairs for signing
	KeyPairTypeECDSA
)


// KeyPair represents an asymmetric key pair stored in the PKCS#11 device.
// It contains both the handle to the private key in the HSM and the public key material.
type KeyPair struct {
	// Handle is the PKCS#11 object handle for the private key
	Handle pkcs11.ObjectHandle
	// Label is the human-readable label for the key pair
	Label string
	// ID is the unique identifier for the key pair (generated from label)
	ID []byte
	// KeyType indicates whether this is an RSA or ECDSA key pair
	KeyType KeyPairType
	// KeySize is the key size in bits (e.g., 2048 for RSA, 256 for P-256)
	KeySize int
	// PublicKey contains the public key material that can be used for verification/encryption
	PublicKey crypto.PublicKey
}


// generateKeyID creates a unique key ID based on the label using SHA256 hash.
// This ensures consistent ID generation for the same label while avoiding
// potential length and character issues with using the label directly as ID.
// Returns the first 16 bytes of the SHA256 hash for compactness.
func generateKeyID(label string) []byte {
	hasher := sha256.New()
	hasher.Write([]byte(label))
	hash := hasher.Sum(nil)
	// Use first 16 bytes of hash to ensure uniqueness while keeping ID compact
	return hash[:16]
}

// GenerateRSAKeyPair generates a new RSA key pair in the PKCS#11 device.
// Supported key sizes are 2048 and 4096 bits.
// The generated keys are marked as non-extractable and sensitive for security.
func (c *Client) GenerateRSAKeyPair(label string, keySize int) (*KeyPair, error) {
	if keySize != 2048 && keySize != 4096 {
		return nil, errors.New("RSA key size must be 2048 or 4096")
	}

	session, err := c.GetSession()
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	keyID := generateKeyID(label)

	publicKeyTemplate := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PUBLIC_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, pkcs11.CKK_RSA),
		pkcs11.NewAttribute(pkcs11.CKA_TOKEN, true),
		pkcs11.NewAttribute(pkcs11.CKA_VERIFY, true),
		pkcs11.NewAttribute(pkcs11.CKA_ENCRYPT, true),
		pkcs11.NewAttribute(pkcs11.CKA_WRAP, false),
		pkcs11.NewAttribute(pkcs11.CKA_MODULUS_BITS, keySize),
		pkcs11.NewAttribute(pkcs11.CKA_PUBLIC_EXPONENT, []byte{0x01, 0x00, 0x01}),
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, label),
		pkcs11.NewAttribute(pkcs11.CKA_ID, keyID),
	}

	privateKeyTemplate := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PRIVATE_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, pkcs11.CKK_RSA),
		pkcs11.NewAttribute(pkcs11.CKA_TOKEN, true),
		pkcs11.NewAttribute(pkcs11.CKA_PRIVATE, true),
		pkcs11.NewAttribute(pkcs11.CKA_SENSITIVE, true),
		pkcs11.NewAttribute(pkcs11.CKA_EXTRACTABLE, false),
		pkcs11.NewAttribute(pkcs11.CKA_SIGN, true),
		pkcs11.NewAttribute(pkcs11.CKA_DECRYPT, true),
		pkcs11.NewAttribute(pkcs11.CKA_UNWRAP, false),
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, label),
		pkcs11.NewAttribute(pkcs11.CKA_ID, keyID),
	}

	pubHandle, privHandle, err := c.ctx.GenerateKeyPair(session, []*pkcs11.Mechanism{pkcs11.NewMechanism(pkcs11.CKM_RSA_PKCS_KEY_PAIR_GEN, nil)}, publicKeyTemplate, privateKeyTemplate)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	publicKey, err := c.extractRSAPublicKey(session, pubHandle)
	if err != nil {
		return nil, errors.Wrap(err, "failed to extract public key")
	}

	return &KeyPair{
		Handle:    privHandle,
		Label:     label,
		ID:        keyID,
		KeyType:   KeyPairTypeRSA,
		KeySize:   keySize,
		PublicKey: publicKey,
	}, nil
}

// GenerateECDSAKeyPair generates a new ECDSA key pair in the PKCS#11 device.
// Supported curves are P-256 and P-384.
// The generated keys are marked as non-extractable and sensitive for security.
func (c *Client) GenerateECDSAKeyPair(label string, curve elliptic.Curve) (*KeyPair, error) {
	var curveOID []byte
	var keySize int

	switch curve {
	case elliptic.P256():
		curveOID = []byte{0x06, 0x08, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07}
		keySize = 256
	case elliptic.P384():
		curveOID = []byte{0x06, 0x05, 0x2b, 0x81, 0x04, 0x00, 0x22}
		keySize = 384
	default:
		return nil, errors.New("unsupported elliptic curve")
	}

	session, err := c.GetSession()
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	keyID := generateKeyID(label)

	publicKeyTemplate := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PUBLIC_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, pkcs11.CKK_ECDSA),
		pkcs11.NewAttribute(pkcs11.CKA_TOKEN, true),
		pkcs11.NewAttribute(pkcs11.CKA_VERIFY, true),
		pkcs11.NewAttribute(pkcs11.CKA_EC_PARAMS, curveOID),
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, label),
		pkcs11.NewAttribute(pkcs11.CKA_ID, keyID),
	}

	privateKeyTemplate := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PRIVATE_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, pkcs11.CKK_ECDSA),
		pkcs11.NewAttribute(pkcs11.CKA_TOKEN, true),
		pkcs11.NewAttribute(pkcs11.CKA_PRIVATE, true),
		pkcs11.NewAttribute(pkcs11.CKA_SENSITIVE, true),
		pkcs11.NewAttribute(pkcs11.CKA_EXTRACTABLE, false),
		pkcs11.NewAttribute(pkcs11.CKA_SIGN, true),
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, label),
		pkcs11.NewAttribute(pkcs11.CKA_ID, keyID),
	}

	pubHandle, privHandle, err := c.ctx.GenerateKeyPair(session, []*pkcs11.Mechanism{pkcs11.NewMechanism(pkcs11.CKM_EC_KEY_PAIR_GEN, nil)}, publicKeyTemplate, privateKeyTemplate)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	publicKey, err := c.extractECDSAPublicKeyWithCurve(session, pubHandle, curve)
	if err != nil {
		return nil, errors.Wrap(err, "failed to extract ECDSA public key")
	}

	return &KeyPair{
		Handle:    privHandle,
		Label:     label,
		ID:        keyID,
		KeyType:   KeyPairTypeECDSA,
		KeySize:   keySize,
		PublicKey: publicKey,
	}, nil
}


// FindKeyPairByLabel searches for a key pair by its label.
// Returns an error if no key is found with the specified label.
func (c *Client) FindKeyPairByLabel(label string) (*KeyPair, error) {
	session, err := c.GetSession()
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	template := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PRIVATE_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, label),
	}

	if err := c.ctx.FindObjectsInit(session, template); err != nil {
		return nil, ConvertPKCS11Error(err)
	}
	defer c.ctx.FindObjectsFinal(session)

	handles, _, err := c.ctx.FindObjects(session, 1)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	if len(handles) == 0 {
		return nil, NewPKCS11Error(ErrKeyNotFound, "key not found", nil)
	}

	return c.getKeyPair(session, handles[0])
}

// FindKeyPairByID searches for a key pair by its unique ID.
// Returns an error if no key is found with the specified ID.
func (c *Client) FindKeyPairByID(id []byte) (*KeyPair, error) {
	session, err := c.GetSession()
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	template := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PRIVATE_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_ID, id),
	}

	if err := c.ctx.FindObjectsInit(session, template); err != nil {
		return nil, ConvertPKCS11Error(err)
	}
	defer c.ctx.FindObjectsFinal(session)

	handles, _, err := c.ctx.FindObjects(session, 1)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	if len(handles) == 0 {
		return nil, NewPKCS11Error(ErrKeyNotFound, "key not found", nil)
	}

	return c.getKeyPair(session, handles[0])
}

// ListKeyPairs returns all key pairs stored in the PKCS#11 device.
// Keys that cannot be processed (due to unsupported types, etc.) are silently skipped.
func (c *Client) ListKeyPairs() ([]*KeyPair, error) {
	session, err := c.GetSession()
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	template := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PRIVATE_KEY),
	}

	if err := c.ctx.FindObjectsInit(session, template); err != nil {
		return nil, ConvertPKCS11Error(err)
	}
	defer c.ctx.FindObjectsFinal(session)

	var keys []*KeyPair
	for {
		handles, more, err := c.ctx.FindObjects(session, 10)
		if err != nil {
			return nil, ConvertPKCS11Error(err)
		}

		for _, handle := range handles {
			keyPair, err := c.getKeyPair(session, handle)
			if err != nil {
				continue
			}
			keys = append(keys, keyPair)
		}

		if !more {
			break
		}
	}

	return keys, nil
}

// getKeyPair retrieves key pair information from a PKCS#11 object handle.
// It extracts key attributes and constructs a KeyPair structure with the public key.
func (c *Client) getKeyPair(session pkcs11.SessionHandle, handle pkcs11.ObjectHandle) (*KeyPair, error) {
	attrs := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, nil),
		pkcs11.NewAttribute(pkcs11.CKA_ID, nil),
		pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, nil),
	}

	attrs, err := c.ctx.GetAttributeValue(session, handle, attrs)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	label := string(attrs[0].Value)
	id := attrs[1].Value
	keyTypeValue := attrs[2].Value

	if len(keyTypeValue) == 0 {
		return nil, errors.New("unable to determine key type")
	}

	var keyType KeyPairType
	var publicKey crypto.PublicKey
	var keySize int

	switch keyTypeValue[0] {
	case byte(pkcs11.CKK_RSA):
		keyType = KeyPairTypeRSA
		pubHandle, err := c.findPublicKeyByID(session, id)
		if err != nil {
			return nil, err
		}
		publicKey, err = c.extractRSAPublicKey(session, pubHandle)
		if err != nil {
			return nil, err
		}
		if rsaPub, ok := publicKey.(*rsa.PublicKey); ok {
			keySize = rsaPub.Size() * 8
		}
	case byte(pkcs11.CKK_ECDSA):
		keyType = KeyPairTypeECDSA
		pubHandle, err := c.findPublicKeyByID(session, id)
		if err != nil {
			return nil, err
		}
		publicKey, err = c.extractECDSAPublicKey(session, pubHandle)
		if err != nil {
			return nil, err
		}
		if ecdsaPub, ok := publicKey.(*ecdsa.PublicKey); ok {
			keySize = ecdsaPub.Curve.Params().BitSize
		}
	default:
		return nil, errors.New("unsupported key type")
	}

	return &KeyPair{
		Handle:    handle,
		Label:     label,
		ID:        id,
		KeyType:   keyType,
		KeySize:   keySize,
		PublicKey: publicKey,
	}, nil
}

// findPublicKeyByID finds the public key object that corresponds to a private key ID.
func (c *Client) findPublicKeyByID(session pkcs11.SessionHandle, id []byte) (pkcs11.ObjectHandle, error) {
	template := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PUBLIC_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_ID, id),
	}

	if err := c.ctx.FindObjectsInit(session, template); err != nil {
		return 0, ConvertPKCS11Error(err)
	}
	defer c.ctx.FindObjectsFinal(session)

	handles, _, err := c.ctx.FindObjects(session, 1)
	if err != nil {
		return 0, ConvertPKCS11Error(err)
	}

	if len(handles) == 0 {
		return 0, NewPKCS11Error(ErrKeyNotFound, "public key not found", nil)
	}

	return handles[0], nil
}

// extractRSAPublicKey extracts RSA public key material from a PKCS#11 public key object.
// It retrieves the modulus and public exponent to construct a Go RSA public key.
func (c *Client) extractRSAPublicKey(session pkcs11.SessionHandle, handle pkcs11.ObjectHandle) (*rsa.PublicKey, error) {
	attrs := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_MODULUS, nil),
		pkcs11.NewAttribute(pkcs11.CKA_PUBLIC_EXPONENT, nil),
	}

	attrs, err := c.ctx.GetAttributeValue(session, handle, attrs)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	n := new(big.Int).SetBytes(attrs[0].Value)
	e := new(big.Int).SetBytes(attrs[1].Value)

	return &rsa.PublicKey{
		N: n,
		E: int(e.Int64()),
	}, nil
}

// extractECDSAPublicKey extracts an ECDSA public key from a public key handle,
// automatically determining the curve from the EC_PARAMS attribute.
// Supports P-256 and P-384 curves.
func (c *Client) extractECDSAPublicKey(session pkcs11.SessionHandle, handle pkcs11.ObjectHandle) (*ecdsa.PublicKey, error) {
	attrs := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_EC_PARAMS, nil),
		pkcs11.NewAttribute(pkcs11.CKA_EC_POINT, nil),
	}

	attrs, err := c.ctx.GetAttributeValue(session, handle, attrs)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	var curve elliptic.Curve
	curveOID := attrs[0].Value

	if len(curveOID) >= 10 &&
		curveOID[0] == 0x06 && curveOID[1] == 0x08 &&
		curveOID[8] == 0x01 && curveOID[9] == 0x07 {
		curve = elliptic.P256()
	} else if len(curveOID) >= 7 &&
		curveOID[0] == 0x06 && curveOID[1] == 0x05 &&
		curveOID[6] == 0x22 {
		curve = elliptic.P384()
	} else {
		return nil, errors.New("unsupported elliptic curve")
	}

	ecPoint := attrs[1].Value
	if len(ecPoint) < 3 || ecPoint[0] != 0x04 {
		return nil, errors.New("invalid EC point format")
	}

	pointLen := (len(ecPoint) - 3) / 2
	xBytes := ecPoint[3 : 3+pointLen]
	yBytes := ecPoint[3+pointLen:]

	x := new(big.Int).SetBytes(xBytes)
	y := new(big.Int).SetBytes(yBytes)

	return &ecdsa.PublicKey{
		Curve: curve,
		X:     x,
		Y:     y,
	}, nil
}

// extractECDSAPublicKeyWithCurve extracts an ECDSA public key from a public key handle,
// using the provided curve parameter instead of auto-detecting from EC_PARAMS.
// This is used during key generation when the curve is already known.
func (c *Client) extractECDSAPublicKeyWithCurve(session pkcs11.SessionHandle, handle pkcs11.ObjectHandle, curve elliptic.Curve) (*ecdsa.PublicKey, error) {
	attrs := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_EC_POINT, nil),
	}

	attrs, err := c.ctx.GetAttributeValue(session, handle, attrs)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	ecPoint := attrs[0].Value
	if len(ecPoint) < 3 || ecPoint[0] != 0x04 {
		return nil, errors.New("invalid EC point format")
	}

	pointLen := (len(ecPoint) - 3) / 2
	xBytes := ecPoint[3 : 3+pointLen]
	yBytes := ecPoint[3+pointLen:]

	x := new(big.Int).SetBytes(xBytes)
	y := new(big.Int).SetBytes(yBytes)

	return &ecdsa.PublicKey{
		Curve: curve,
		X:     x,
		Y:     y,
	}, nil
}

// ImportRSAKeyPair imports an existing RSA private key into the PKCS#11 device.
// The imported key is marked as non-extractable and sensitive for security.
// Both the private and public key objects are created in the device.
func (c *Client) ImportRSAKeyPair(label string, privateKey *rsa.PrivateKey) (*KeyPair, error) {
	if privateKey == nil {
		return nil, errors.New("private key cannot be nil")
	}

	session, err := c.GetSession()
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	keyID := generateKeyID(label)

	// Import private key
	privateKeyTemplate := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PRIVATE_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, pkcs11.CKK_RSA),
		pkcs11.NewAttribute(pkcs11.CKA_TOKEN, true),
		pkcs11.NewAttribute(pkcs11.CKA_PRIVATE, true),
		pkcs11.NewAttribute(pkcs11.CKA_SENSITIVE, true),
		pkcs11.NewAttribute(pkcs11.CKA_EXTRACTABLE, false),
		pkcs11.NewAttribute(pkcs11.CKA_SIGN, true),
		pkcs11.NewAttribute(pkcs11.CKA_DECRYPT, true),
		pkcs11.NewAttribute(pkcs11.CKA_UNWRAP, false),
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, label),
		pkcs11.NewAttribute(pkcs11.CKA_ID, keyID),
		pkcs11.NewAttribute(pkcs11.CKA_MODULUS, privateKey.N.Bytes()),
		pkcs11.NewAttribute(pkcs11.CKA_PUBLIC_EXPONENT, big.NewInt(int64(privateKey.E)).Bytes()),
		pkcs11.NewAttribute(pkcs11.CKA_PRIVATE_EXPONENT, privateKey.D.Bytes()),
		pkcs11.NewAttribute(pkcs11.CKA_PRIME_1, privateKey.Primes[0].Bytes()),
		pkcs11.NewAttribute(pkcs11.CKA_PRIME_2, privateKey.Primes[1].Bytes()),
		pkcs11.NewAttribute(pkcs11.CKA_EXPONENT_1, privateKey.Precomputed.Dp.Bytes()),
		pkcs11.NewAttribute(pkcs11.CKA_EXPONENT_2, privateKey.Precomputed.Dq.Bytes()),
		pkcs11.NewAttribute(pkcs11.CKA_COEFFICIENT, privateKey.Precomputed.Qinv.Bytes()),
	}

	privHandle, err := c.ctx.CreateObject(session, privateKeyTemplate)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	// Import corresponding public key
	publicKeyTemplate := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PUBLIC_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, pkcs11.CKK_RSA),
		pkcs11.NewAttribute(pkcs11.CKA_TOKEN, true),
		pkcs11.NewAttribute(pkcs11.CKA_VERIFY, true),
		pkcs11.NewAttribute(pkcs11.CKA_ENCRYPT, true),
		pkcs11.NewAttribute(pkcs11.CKA_WRAP, false),
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, label),
		pkcs11.NewAttribute(pkcs11.CKA_ID, keyID),
		pkcs11.NewAttribute(pkcs11.CKA_MODULUS, privateKey.N.Bytes()),
		pkcs11.NewAttribute(pkcs11.CKA_PUBLIC_EXPONENT, big.NewInt(int64(privateKey.E)).Bytes()),
	}

	_, err = c.ctx.CreateObject(session, publicKeyTemplate)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	return &KeyPair{
		Handle:    privHandle,
		Label:     label,
		ID:        keyID,
		KeyType:   KeyPairTypeRSA,
		KeySize:   privateKey.Size() * 8,
		PublicKey: &privateKey.PublicKey,
	}, nil
}

// ImportECDSAKeyPair imports an existing ECDSA private key into the PKCS#11 device.
// The imported key is marked as non-extractable and sensitive for security.
// Both the private and public key objects are created in the device.
func (c *Client) ImportECDSAKeyPair(label string, privateKey *ecdsa.PrivateKey) (*KeyPair, error) {
	if privateKey == nil {
		return nil, errors.New("private key cannot be nil")
	}

	session, err := c.GetSession()
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	var curveOID []byte
	var keySize int

	switch privateKey.Curve {
	case elliptic.P256():
		curveOID = []byte{0x06, 0x08, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07}
		keySize = 256
	case elliptic.P384():
		curveOID = []byte{0x06, 0x05, 0x2b, 0x81, 0x04, 0x00, 0x22}
		keySize = 384
	default:
		return nil, errors.New("unsupported elliptic curve")
	}

	keyID := generateKeyID(label)

	// Convert private key scalar to bytes
	privateKeyBytes := privateKey.D.Bytes()

	// Import private key
	privateKeyTemplate := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PRIVATE_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, pkcs11.CKK_ECDSA),
		pkcs11.NewAttribute(pkcs11.CKA_TOKEN, true),
		pkcs11.NewAttribute(pkcs11.CKA_PRIVATE, true),
		pkcs11.NewAttribute(pkcs11.CKA_SENSITIVE, true),
		pkcs11.NewAttribute(pkcs11.CKA_EXTRACTABLE, false),
		pkcs11.NewAttribute(pkcs11.CKA_SIGN, true),
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, label),
		pkcs11.NewAttribute(pkcs11.CKA_ID, keyID),
		pkcs11.NewAttribute(pkcs11.CKA_EC_PARAMS, curveOID),
		pkcs11.NewAttribute(pkcs11.CKA_VALUE, privateKeyBytes),
	}

	privHandle, err := c.ctx.CreateObject(session, privateKeyTemplate)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	// Create EC point for public key
	// Format: 0x04 + X coordinate + Y coordinate
	coordSize := (keySize + 7) / 8
	ecPoint := make([]byte, 1+2*coordSize)
	ecPoint[0] = 0x04

	xBytes := privateKey.X.Bytes()
	yBytes := privateKey.Y.Bytes()

	copy(ecPoint[1+coordSize-len(xBytes):1+coordSize], xBytes)
	copy(ecPoint[1+2*coordSize-len(yBytes):], yBytes)

	// Wrap EC point in OCTET STRING for PKCS#11
	ecPointWrapped := append([]byte{0x04, byte(len(ecPoint))}, ecPoint...)

	// Import corresponding public key
	publicKeyTemplate := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_CLASS, pkcs11.CKO_PUBLIC_KEY),
		pkcs11.NewAttribute(pkcs11.CKA_KEY_TYPE, pkcs11.CKK_ECDSA),
		pkcs11.NewAttribute(pkcs11.CKA_TOKEN, true),
		pkcs11.NewAttribute(pkcs11.CKA_VERIFY, true),
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, label),
		pkcs11.NewAttribute(pkcs11.CKA_ID, keyID),
		pkcs11.NewAttribute(pkcs11.CKA_EC_PARAMS, curveOID),
		pkcs11.NewAttribute(pkcs11.CKA_EC_POINT, ecPointWrapped),
	}

	_, err = c.ctx.CreateObject(session, publicKeyTemplate)
	if err != nil {
		return nil, ConvertPKCS11Error(err)
	}

	return &KeyPair{
		Handle:    privHandle,
		Label:     label,
		ID:        keyID,
		KeyType:   KeyPairTypeECDSA,
		KeySize:   keySize,
		PublicKey: &privateKey.PublicKey,
	}, nil
}

// ImportKeyPair imports a private key into the PKCS#11 device.
// It automatically detects the key type (RSA or ECDSA) and calls the appropriate import function.
func (c *Client) ImportKeyPair(label string, privateKey crypto.PrivateKey) (*KeyPair, error) {
	switch key := privateKey.(type) {
	case *rsa.PrivateKey:
		return c.ImportRSAKeyPair(label, key)
	case *ecdsa.PrivateKey:
		return c.ImportECDSAKeyPair(label, key)
	default:
		return nil, errors.New("unsupported private key type")
	}
}

// String returns a string representation of the key pair.
// String returns a string representation of the key pair with label, type, and size.
func (k *KeyPair) String() string {
	return fmt.Sprintf("Key{Label: %s, Type: %v, Size: %d}", k.Label, k.KeyType, k.KeySize)
}
