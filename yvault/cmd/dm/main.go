package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/ThalesGroup/crypto11"
	"github.com/qcu266/labs/yvault/pkg/dm"
	"golang.org/x/crypto/ssh"
)

func main() {
	var (
		host               = flag.String("host", "", "SSH host (e.g., user@hostname)")
		port               = flag.Int("port", 22, "SSH port")
		identity           = flag.String("identity", "", "Private key path (defaults to ~/.ssh/id_rsa)")
		action             = flag.String("action", "list", "Action to perform: list, add, delete, get-sn, get-mac")
		key                = flag.String("key", "", "Public key string or path to public key file to add/delete")
		pkcs11Provider     = flag.String("pkcs11-provider", "", "Path to the PKCS#11 module/library")
		pkcs11Pin          = flag.String("pkcs11-pin", "", "PKCS#11 token PIN")
		pkcs11KeyLabel     = flag.String("pkcs11-key-label", "", "Label of the key on the PKCS#11 token")
		authorizedKeysPath = flag.String("authorized-keys-path", "/.ssh/authorized_keys", "Path to the authorized_keys file on the remote server")
	)
	flag.Parse()

	if *host == "" {
		log.Fatal("Host (-host) is required")
	}

	parts := strings.SplitN(*host, "@", 2)
	if len(parts) != 2 {
		log.Fatalf("Invalid host format. Expected user@hostname, got %s", *host)
	}
	user, hostAddr := parts[0], parts[1]

	authMethods := []ssh.AuthMethod{}
	var p11 *crypto11.Context
	var err error

	if *pkcs11Provider != "" {
		var authMethod ssh.AuthMethod
		authMethod, p11, err = createP11AuthMethod(*pkcs11Provider, *pkcs11Pin, *pkcs11KeyLabel)
		if err != nil {
			log.Fatalf("Failed to create PKCS#11 auth method: %v", err)
		}
		authMethods = append(authMethods, authMethod)
		defer p11.Close()
	} else {
		authMethod, err := createIdentityAuthMethod(*identity)
		if err != nil {
			log.Fatalf("Failed to create identity auth method: %v", err)
		}
		authMethods = append(authMethods, authMethod)
	}

	cfg := &dm.ManagerConfig{
		Host:        hostAddr,
		Port:        *port,
		User:        user,
		AuthMethods: authMethods,

		AuthorizedKeysPath: *authorizedKeysPath,
	}

	manager, err := dm.NewDeviceManager(cfg)
	if err != nil {
		log.Fatalf("Failed to create SSH key manager: %v", err)
	}
	defer manager.Close()

	switch *action {
	case "list":
		keys, err := manager.ListKeys()
		if err != nil {
			log.Fatalf("Failed to list authorized keys: %v", err)
		}
		fmt.Println("Authorized keys:")
		for _, k := range keys {
			fmt.Println(k)
		}
	case "add":
		if *key == "" {
			log.Fatal("Public key (-key) is required for 'add' action")
		}
		keyContent, err := getKeyContent(*key)
		if err != nil {
			log.Fatalf("Failed to get key content: %v", err)
		}
		if err := manager.AddKey(keyContent); err != nil {
			log.Fatalf("Failed to add authorized key: %v", err)
		}
		fmt.Println("Key added successfully.")
	case "delete":
		if *key == "" {
			log.Fatal("Public key (-key) is required for 'delete' action")
		}
		keyContent, err := getKeyContent(*key)
		if err != nil {
			log.Fatalf("Failed to get key content: %v", err)
		}
		if err := manager.DeleteKey(keyContent); err != nil {
			log.Fatalf("Failed to delete authorized key: %v", err)
		}
		fmt.Println("Key deleted successfully.")
	case "get-sn":
		sn, err := manager.GetDeviceSN()
		if err != nil {
			log.Fatalf("Failed to get device SN: %v", err)
		}
		fmt.Println("Device SN:", sn)
	case "get-mac":
		mac, err := manager.GetDeviceMAC()
		if err != nil {
			log.Fatalf("Failed to get device MAC: %v", err)
		}
		fmt.Println("Device MAC:", mac)
	default:
		log.Fatalf("Unknown action: %s. Valid actions are: list, add, delete, get-sn, get-mac", *action)
	}
}

func getKeyContent(key string) (string, error) {
	// 检查密钥是否为文件路径
	if _, err := os.Stat(key); err == nil {
		content, err := os.ReadFile(key)
		if err != nil {
			return "", fmt.Errorf("failed to read key file %s: %v", key, err)
		}
		return strings.TrimSpace(string(content)), nil
	}
	// 否则，将其视为原始密钥字符串
	return strings.TrimSpace(key), nil
}

func createP11AuthMethod(pkcs11Provider string, pkcs11Pin string, pkcs11KeyLabel string) (ssh.AuthMethod, *crypto11.Context, error) {
	// 修复未定义cfg变量的问题，直接使用参数
	if pkcs11Provider == "" {
		return nil, nil, fmt.Errorf("PKCS#11 provider (-pkcs11-provider) is required")
	}
	if pkcs11Pin == "" {
		return nil, nil, fmt.Errorf("PKCS#11 PIN (-pkcs11-pin) is required")
	}
	if pkcs11KeyLabel == "" {
		return nil, nil, fmt.Errorf("PKCS#11 key label (-pkcs11-key-label) is required")
	}

	slotNumber := 0
	p11, err := crypto11.Configure(&crypto11.Config{
		Path:       pkcs11Provider,
		SlotNumber: &slotNumber,
		Pin:        pkcs11Pin,
	})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to configure PKCS#11: %w", err)
	}

	// 不在这里 defer p11.Close()，而是返回 p11 让调用者管理

	privKey, err := p11.FindKeyPair(nil, []byte(pkcs11KeyLabel))
	if err != nil {
		p11.Close() // 出错时清理资源
		return nil, nil, fmt.Errorf("failed to find key pair with label '%s': %w", pkcs11KeyLabel, err)
	}
	if privKey == nil {
		p11.Close() // 出错时清理资源
		return nil, nil, fmt.Errorf("key pair with label '%s' not found", pkcs11KeyLabel)
	}

	signer, err := ssh.NewSignerFromKey(privKey)
	if err != nil {
		p11.Close() // 出错时清理资源
		return nil, nil, fmt.Errorf("failed to create signer from PKCS#11 key: %w", err)
	}
	return ssh.PublicKeys(signer), p11, nil
}

func createIdentityAuthMethod(identityFile string) (ssh.AuthMethod, error) {

	if identityFile == "" {
		home, err := os.UserHomeDir()
		if err != nil {
			return nil, fmt.Errorf("failed to get user home directory: %v", err)
		}
		identityFile = filepath.Join(home, ".ssh", "id_rsa")
	}
	key, err := os.ReadFile(identityFile)
	if err != nil {
		return nil, fmt.Errorf("unable to read private key: %v", err)
	}

	signer, err := ssh.ParsePrivateKey(key)
	if err != nil {
		return nil, fmt.Errorf("unable to parse private key: %v", err)
	}
	return ssh.PublicKeys(signer), nil
}
