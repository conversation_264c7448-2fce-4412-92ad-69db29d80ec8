package main

import (
	"crypto"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"testing"

	"github.com/ThalesGroup/crypto11"
	"golang.org/x/crypto/ssh"
)

func getP11Signer(t *testing.T) ssh.Signer {

	slotNumber := 0
	p11, err := crypto11.Configure(&crypto11.Config{
		Path:       "/home/<USER>/hsm/libcs_pkcs11_R3.so",
		SlotNumber: &slotNumber,
		Pin:        "12345678",
	})
	if err != nil {
		t.Fatalf("failed to configure PKCS#11: %v", err)
	}

	// privKey, err := p11.FindKeyPair([]byte("100"), nil)
	// privKey, err := p11.FindRSAPrivateKey(nil, []byte("demo-ssh-key"))
	privKey, err := p11.FindKeyPair(nil, []byte("demo-ssh-key"))
	if err != nil {
		t.Fatalf("failed to find key pair with label '%s': %v", "ssh-key", err)
	}
	if privKey == nil {
		t.Fatalf("key pair with label '%s' not found", "ssh-key")
	}

	signer, err := ssh.NewSignerFromKey(privKey)
	if err != nil {
		t.Fatalf("failed to create signer from PKCS#11 key: %v", err)
	}
	return signer
}

func getIdentitySigner(t *testing.T) ssh.Signer {

	identityFile := "/home/<USER>/hsm/ssh/id_rsa"
	key, err := os.ReadFile(identityFile)
	if err != nil {
		t.Fatalf("unable to read private key: %v", err)
	}
	signer, err := ssh.ParsePrivateKey(key)
	if err != nil {
		t.Fatalf("unable to parse private key: %v", err)
	}
	return signer
}

type testSigner struct {
	privateKey crypto11.PrivateKey
}

func (s *testSigner) Public() crypto.PublicKey {
	return nil
}

func (s *testSigner) Sign(rand io.Reader, digest []byte, opts crypto.SignerOpts) ([]byte, error) {
	return s.privateKey.Sign(rand, digest, opts)
}

func TestCreateAuthMethod(t *testing.T) {

	p11Signer := getP11Signer(t)

	p11Signature, err := p11Signer.Sign(nil, []byte("test"))
	if err != nil {
		t.Fatalf("failed to sign: %v", err)
	}
	fmt.Println(base64.StdEncoding.EncodeToString(p11Signature.Blob))

	p11Signature2, err := p11Signer.Sign(nil, []byte("test"))
	if err != nil {
		t.Fatalf("failed to sign: %v", err)
	}
	fmt.Println(base64.StdEncoding.EncodeToString(p11Signature2.Blob))

	identitySigner := getIdentitySigner(t)
	identitySignature, err := identitySigner.Sign(nil, []byte("test"))
	if err != nil {
		t.Fatalf("failed to sign: %v", err)
	}
	fmt.Println(base64.StdEncoding.EncodeToString(identitySignature.Blob))

	if err := identitySigner.PublicKey().Verify([]byte("test"), p11Signature); err != nil {
		t.Fatalf("failed to verify: %v", err)
	}

	// if !bytes.Equal(p11Signature.Blob, identitySignature.Blob) {
	// 	t.Fatalf("p11Signature and identitySignature are not the same")
	// }

	// authMethods := []ssh.AuthMethod{
	// 	ssh.PublicKeys(p11Signer),
	// 	ssh.PublicKeys(identitySigner),
	// }

}
