# SoftHSMv2 Cross-Platform Build Scripts

This repository contains cross-platform build scripts for compiling SoftHSMv2 from source code. The compiled binaries will be installed in the `build` directory relative to the script location.

## Features

- **Cross-platform support**: Linux, macOS, and Windows
- **Automatic dependency checking**: Verifies required build tools
- **Parallel compilation**: Uses all available CPU cores for faster builds
- **Self-contained installation**: Installs to local `build` directory
- **Environment setup scripts**: Easy configuration for using SoftHSMv2

## Prerequisites

### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install build-essential autoconf automake libtool pkg-config libssl-dev wget
```

### Linux (CentOS/RHEL/Fedora)
```bash
# CentOS/RHEL 7
sudo yum install gcc gcc-c++ make autoconf automake libtool pkgconfig openssl-devel wget

# CentOS/RHEL 8+ or Fedora
sudo dnf install gcc gcc-c++ make autoconf automake libtool pkgconfig openssl-devel wget
```

### macOS
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install autoconf automake libtool pkg-config openssl wget
```

### Windows
Install one of the following environments:

1. **MSYS2** (Recommended)
   - Download and install from: https://www.msys2.org/
   - Open MSYS2 terminal and run:
   ```bash
   pacman -S base-devel mingw-w64-x86_64-toolchain
   pacman -S mingw-w64-x86_64-autotools mingw-w64-x86_64-openssl
   ```

2. **WSL (Windows Subsystem for Linux)**
   - Install WSL2 from Microsoft Store
   - Follow Linux instructions above

3. **Git Bash** (Limited support)
   - Install Git for Windows which includes Git Bash
   - May require additional manual setup

## Usage

### Linux and macOS

1. Make the script executable:
   ```bash
   chmod +x build_softhsm.sh
   ```

2. Run the build script:
   ```bash
   ./build_softhsm.sh
   ```

3. Set up the environment:
   ```bash
   source build/setup_env.sh
   ```

### Windows

#### Using PowerShell (Recommended)

1. Open PowerShell as Administrator (if needed)

2. Allow script execution (if not already enabled):
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. Run the build script:
   ```powershell
   .\build_softhsm.ps1
   ```

4. Set up the environment:
   ```powershell
   . .\build\setup_env.ps1
   ```

#### Using MSYS2/Git Bash

1. Open MSYS2 or Git Bash terminal

2. Run the bash script:
   ```bash
   ./build_softhsm.sh
   ```

3. Set up the environment:
   ```bash
   source build/setup_env.sh
   ```

## Script Options

### PowerShell Script Options

```powershell
# Build specific version
.\build_softhsm.ps1 -Version "2.6.0"

# Clean build directory before building
.\build_softhsm.ps1 -Clean

# Show help
.\build_softhsm.ps1 -Help
```

## Directory Structure

After successful build, the directory structure will be:

```
.
├── build_softhsm.sh          # Bash build script
├── build_softhsm.ps1         # PowerShell build script
├── BUILD_README.md           # This file
└── build/                    # Installation directory
    ├── bin/                  # SoftHSMv2 binaries
    │   ├── softhsm2-util     # Token management utility
    │   └── ...
    ├── lib/                  # Libraries
    │   ├── softhsm/          # SoftHSMv2 PKCS#11 library
    │   └── ...
    ├── etc/                  # Configuration files
    ├── tokens/               # Token storage directory
    ├── softhsm2.conf         # Default configuration
    ├── setup_env.sh          # Environment setup (Unix)
    ├── setup_env.bat         # Environment setup (Windows batch)
    └── setup_env.ps1         # Environment setup (Windows PowerShell)
```

## Using SoftHSMv2

After building and setting up the environment:

1. **Initialize a token**:
   ```bash
   softhsm2-util --init-token --slot 0 --label "MyToken"
   ```

2. **List available tokens**:
   ```bash
   softhsm2-util --show-slots
   ```

3. **Import a key**:
   ```bash
   softhsm2-util --import key.pem --slot 0 --label "MyKey" --id 01
   ```

4. **Use with applications**:
   - The PKCS#11 library is located at: `build/lib/softhsm/libsofthsm2.so` (Linux/macOS) or `build/lib/softhsm/libsofthsm2.dll` (Windows)
   - Configuration file: `build/softhsm2.conf`

## Troubleshooting

### Common Issues

1. **Missing dependencies**: The script will check for required dependencies and provide installation instructions.

2. **Permission errors**: Ensure you have write permissions in the script directory.

3. **Network issues**: If download fails, check your internet connection and firewall settings.

4. **Windows path issues**: Use forward slashes or properly escaped backslashes in paths.

### Build Logs

Build logs are displayed in real-time. If the build fails:

1. Check the error messages for missing dependencies
2. Ensure all prerequisites are installed
3. Try running with elevated privileges if needed
4. Check available disk space

### Getting Help

If you encounter issues:

1. Check the error messages carefully
2. Verify all prerequisites are installed
3. Try building with a clean environment (`-Clean` option)
4. Check SoftHSMv2 official documentation: https://www.opendnssec.org/softhsm/

## Configuration

The default configuration file (`build/softhsm2.conf`) can be customized:

```ini
# SoftHSM v2 configuration file
directories.tokendir = ./tokens/
objectstore.backend = file
log.level = INFO
slots.removable = false
```

Adjust the `directories.tokendir` path and other settings as needed for your environment.

## License

SoftHSMv2 is licensed under the BSD 2-Clause License. See the SoftHSMv2 source code for license details.

These build scripts are provided as-is for convenience and are not officially associated with the SoftHSMv2 project.
